graph TD
    A[Client] -->|API Calls| B[API Gateway]
    B -->|Routes Requests| C[LivingTalk Microservice]
    B -->|Routes Requests| D[PigCloud Microservice]
    
    C -->|Action/Model Calls| E[Digital Human Engine]
    C -->|Configuration Fetch| F[Shared NFS Directory]
    C -->|LLM Calls| G[Pig Platform LLM]
    
    D -->|Data Persistence| H[Database]
    D -->|Store/Retrieve Config| F
    D -->|RBAC Control| I[Cloud-DHuman RBAC]
    
    E -->|Model Processing| J[Action Models]
    E -->|Avatar Rendering| K[Avatar Models]
    
    G -->|Knowledge Base Access| L[Knowledge Base]
    
    subgraph LivingTalk
        C
        E
        J
        K
    end
    
    subgraph PigCloud
        D
        H
        I
        G
        L
    end
    
    F -->|Shared Config Files| D