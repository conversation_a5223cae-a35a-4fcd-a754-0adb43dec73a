# FunASR服务Docker镜像
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    ffmpeg \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements_funasr.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements_funasr.txt

# 复制应用代码
COPY funasr_server.py .

# 创建模型缓存目录
RUN mkdir -p /app/models /app/cache

# 设置环境变量
ENV HF_HOME=/app/cache
ENV TRANSFORMERS_CACHE=/app/cache
ENV FUNASR_CACHE_DIR=/app/cache

# 暴露端口
EXPOSE 10096

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:10096/health || exit 1

# 启动命令
CMD ["python", "funasr_server.py", "--host", "0.0.0.0", "--port", "10096"]
