# 基于原有的LiveTalking镜像
FROM livetalking:base

# 安装Nacos相关依赖
RUN pip install --no-cache-dir \
    nacos-sdk-python>=0.1.6 \
    pyyaml>=6.0 \
    requests>=2.25.0

# 复制Nacos配置管理相关文件
COPY multi_session_enhancement/ /app/multi_session_enhancement/

# 设置环境变量
ENV NACOS_SERVER_ADDRESSES=nacos:8848
ENV NACOS_NAMESPACE=""
ENV NACOS_USERNAME=nacos
ENV NACOS_PASSWORD=nacos

# 暴露端口
EXPOSE 8010

# 启动命令
CMD ["python", "multi_session_enhancement/enhanced_app.py", "--enable_nacos"]
