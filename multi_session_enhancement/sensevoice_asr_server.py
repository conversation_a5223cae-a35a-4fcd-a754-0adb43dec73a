"""
SenseVoice ASR服务 - 基于vLLM部署的SenseVoice Small模型
支持WebSocket实时识别和HTTP API接口
"""
import asyncio
import json
import logging
import time
import wave
import io
import base64
from typing import Dict, List, Optional, Any
import numpy as np
import requests
import aiohttp
from aiohttp import web, WSMsgType
import websockets
from dataclasses import dataclass, asdict

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SenseVoiceConfig:
    """SenseVoice配置"""
    vllm_base_url: str = "http://localhost:8000"  # vLLM服务地址
    model_name: str = "sensevoice-small"          # 模型名称
    language: str = "auto"                        # 语言设置 auto/zh/en/ja/ko
    use_itn: bool = True                         # 是否使用逆文本标准化
    chunk_size: int = 960                        # 音频块大小 (60ms at 16kHz)
    sample_rate: int = 16000                     # 采样率
    max_audio_length: int = 30                   # 最大音频长度(秒)
    timeout: float = 10.0                        # 请求超时时间

class AudioBuffer:
    """音频缓冲区管理"""
    
    def __init__(self, sample_rate: int = 16000, max_duration: int = 30):
        self.sample_rate = sample_rate
        self.max_samples = sample_rate * max_duration
        self.buffer = np.array([], dtype=np.int16)
        self.last_activity = time.time()
    
    def add_audio(self, audio_data: bytes):
        """添加音频数据"""
        # 将bytes转换为int16数组
        audio_array = np.frombuffer(audio_data, dtype=np.int16)
        self.buffer = np.concatenate([self.buffer, audio_array])
        
        # 限制缓冲区大小
        if len(self.buffer) > self.max_samples:
            self.buffer = self.buffer[-self.max_samples:]
        
        self.last_activity = time.time()
    
    def get_audio_wav(self) -> bytes:
        """获取WAV格式音频"""
        if len(self.buffer) == 0:
            return b''
        
        # 创建WAV文件
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(self.sample_rate)
            wav_file.writeframes(self.buffer.tobytes())
        
        return wav_buffer.getvalue()
    
    def clear(self):
        """清空缓冲区"""
        self.buffer = np.array([], dtype=np.int16)
    
    def duration(self) -> float:
        """获取音频时长(秒)"""
        return len(self.buffer) / self.sample_rate
    
    def is_silent(self, threshold: float = 0.01) -> bool:
        """检测是否静音"""
        if len(self.buffer) == 0:
            return True
        
        # 计算音频能量
        energy = np.mean(np.abs(self.buffer.astype(np.float32))) / 32768.0
        return energy < threshold

class SenseVoiceASRService:
    """SenseVoice ASR服务"""
    
    def __init__(self, config: SenseVoiceConfig):
        self.config = config
        self.session = None
        self.active_sessions: Dict[str, Dict] = {}
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.timeout))
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def transcribe_audio(self, audio_data: bytes, language: str = None) -> str:
        """转录音频"""
        try:
            # 准备请求数据
            language = language or self.config.language
            
            # 将音频转换为base64
            audio_b64 = base64.b64encode(audio_data).decode('utf-8')
            
            # 构建请求
            payload = {
                "model": self.config.model_name,
                "audio": audio_b64,
                "language": language,
                "use_itn": self.config.use_itn,
                "response_format": "json"
            }
            
            # 发送请求到vLLM服务
            url = f"{self.config.vllm_base_url}/v1/audio/transcriptions"
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('text', '')
                else:
                    error_text = await response.text()
                    logger.error(f"SenseVoice API错误: {response.status} - {error_text}")
                    return ""
                    
        except Exception as e:
            logger.error(f"音频转录失败: {e}")
            return ""
    
    async def handle_websocket(self, request):
        """处理WebSocket连接"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        session_id = request.query.get('session_id', f"session_{int(time.time())}")
        logger.info(f"新的WebSocket连接: {session_id}")
        
        # 初始化会话
        audio_buffer = AudioBuffer(self.config.sample_rate, self.config.max_audio_length)
        session_info = {
            'ws': ws,
            'buffer': audio_buffer,
            'config': None,
            'last_transcription': time.time(),
            'is_speaking': False
        }
        self.active_sessions[session_id] = session_info
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    # 处理JSON配置消息
                    try:
                        data = json.loads(msg.data)
                        await self._handle_config_message(session_id, data)
                    except json.JSONDecodeError:
                        logger.error(f"无效的JSON消息: {msg.data}")
                
                elif msg.type == WSMsgType.BINARY:
                    # 处理音频数据
                    await self._handle_audio_message(session_id, msg.data)
                
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket错误: {ws.exception()}')
                    break
        
        except Exception as e:
            logger.error(f"WebSocket处理错误: {e}")
        
        finally:
            # 清理会话
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            logger.info(f"WebSocket连接关闭: {session_id}")
        
        return ws
    
    async def _handle_config_message(self, session_id: str, data: Dict[str, Any]):
        """处理配置消息"""
        session = self.active_sessions[session_id]
        session['config'] = data
        
        logger.info(f"会话 {session_id} 配置: {data}")
        
        # 发送确认消息
        response = {
            "status": "connected",
            "message": "SenseVoice ASR服务已连接",
            "config": data
        }
        await session['ws'].send_text(json.dumps(response))
    
    async def _handle_audio_message(self, session_id: str, audio_data: bytes):
        """处理音频消息"""
        session = self.active_sessions[session_id]
        buffer = session['buffer']
        
        # 添加音频到缓冲区
        buffer.add_audio(audio_data)
        session['is_speaking'] = not buffer.is_silent()
        
        # 检查是否需要进行转录
        current_time = time.time()
        time_since_last = current_time - session['last_transcription']
        
        # 转录条件：
        # 1. 缓冲区有足够的音频 (>= 2秒)
        # 2. 距离上次转录超过1秒
        # 3. 检测到静音间隔
        should_transcribe = (
            buffer.duration() >= 2.0 and 
            time_since_last >= 1.0 and
            (buffer.is_silent() or buffer.duration() >= 5.0)
        )
        
        if should_transcribe:
            await self._perform_transcription(session_id)
    
    async def _perform_transcription(self, session_id: str):
        """执行转录"""
        session = self.active_sessions[session_id]
        buffer = session['buffer']
        config = session['config'] or {}
        
        if buffer.duration() < 0.5:  # 音频太短，跳过
            return
        
        try:
            # 获取音频数据
            wav_data = buffer.get_audio_wav()
            
            # 执行转录
            text = await self.transcribe_audio(wav_data, config.get('language'))
            
            if text.strip():
                # 发送转录结果
                response = {
                    "text": text,
                    "mode": config.get('mode', 'online'),
                    "is_final": True,
                    "timestamp": [],
                    "duration": buffer.duration(),
                    "session_id": session_id
                }
                
                await session['ws'].send_text(json.dumps(response))
                logger.info(f"转录结果 [{session_id}]: {text}")
            
            # 清空缓冲区
            buffer.clear()
            session['last_transcription'] = time.time()
            
        except Exception as e:
            logger.error(f"转录执行失败: {e}")
    
    async def transcribe_file(self, request):
        """文件转录API"""
        try:
            reader = await request.multipart()
            audio_file = None
            language = self.config.language
            
            async for field in reader:
                if field.name == 'audio':
                    audio_file = await field.read()
                elif field.name == 'language':
                    language = await field.text()
            
            if not audio_file:
                return web.json_response({"error": "未找到音频文件"}, status=400)
            
            # 转录音频
            text = await self.transcribe_audio(audio_file, language)
            
            return web.json_response({
                "text": text,
                "language": language,
                "model": self.config.model_name,
                "success": True
            })
            
        except Exception as e:
            logger.error(f"文件转录失败: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def health_check(self, request):
        """健康检查"""
        try:
            # 测试vLLM服务连接
            url = f"{self.config.vllm_base_url}/health"
            async with self.session.get(url) as response:
                vllm_status = response.status == 200
        except:
            vllm_status = False
        
        return web.json_response({
            "status": "healthy" if vllm_status else "unhealthy",
            "vllm_connection": vllm_status,
            "active_sessions": len(self.active_sessions),
            "config": asdict(self.config)
        })

def create_app(config: SenseVoiceConfig) -> web.Application:
    """创建ASR服务应用"""
    app = web.Application()
    
    # 创建ASR服务实例
    asr_service = SenseVoiceASRService(config)
    
    # 设置路由
    app.router.add_get('/ws', asr_service.handle_websocket)
    app.router.add_post('/v1/audio/transcriptions', asr_service.transcribe_file)
    app.router.add_get('/health', asr_service.health_check)
    
    # 静态文件服务 (可选)
    app.router.add_static('/', path='web/asr', name='asr_web')
    
    return app

async def main():
    """主函数"""
    # 配置
    config = SenseVoiceConfig(
        vllm_base_url="http://localhost:8000",  # 你的vLLM服务地址
        model_name="sensevoice-small",
        language="auto",
        use_itn=True
    )
    
    # 创建应用
    app = create_app(config)
    
    # 启动服务
    runner = web.AppRunner(app)
    await runner.setup()
    
    site = web.TCPSite(runner, '0.0.0.0', 10096)  # 使用FunASR兼容端口
    await site.start()
    
    logger.info(f"SenseVoice ASR服务启动在 http://0.0.0.0:10096")
    logger.info(f"vLLM服务地址: {config.vllm_base_url}")
    logger.info(f"WebSocket地址: ws://localhost:10096/ws")
    logger.info(f"HTTP API地址: http://localhost:10096/v1/audio/transcriptions")
    
    # 保持服务运行
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("服务停止")
    finally:
        await runner.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
