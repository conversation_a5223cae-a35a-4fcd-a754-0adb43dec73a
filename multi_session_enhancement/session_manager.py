"""
会话管理器 - 管理多个会话的生命周期和配置
"""
import asyncio
import threading
from typing import Dict, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import json
import logging

logger = logging.getLogger(__name__)

@dataclass
class SessionConfig:
    """会话配置类"""
    session_id: int
    avatar_id: str = "avator_1"
    model: str = "musetalk"  # musetalk, wav2lip, ultralight
    tts: str = "edgetts"     # edgetts, gpt-sovits, xtts, cosyvoice, fishtts, tencent, doubao
    voice: str = "zh-CN-YunxiaNeural"  # 声音参数
    ref_text: Optional[str] = None
    tts_server: str = "http://127.0.0.1:9880"
    batch_size: int = 16
    fps: int = 50
    W: int = 450
    H: int = 450
    customvideo_config: str = ""
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        # 转换datetime为字符串
        if self.created_at:
            data['created_at'] = self.created_at.isoformat()
        if self.updated_at:
            data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SessionConfig':
        """从字典创建配置对象"""
        # 处理datetime字段
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        return cls(**data)

class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        self.sessions: Dict[int, SessionConfig] = {}
        self.session_instances: Dict[int, Any] = {}  # 存储实际的nerfreal实例
        self.lock = threading.RLock()
        self.default_config = SessionConfig(session_id=0)
    
    def create_session(self, session_id: int, config: Optional[Dict[str, Any]] = None) -> SessionConfig:
        """创建新会话"""
        with self.lock:
            if session_id in self.sessions:
                logger.warning(f"Session {session_id} already exists, updating config")
            
            # 创建会话配置
            session_config = self._create_session_config(session_id, config)
            self.sessions[session_id] = session_config
            
            logger.info(f"Created session {session_id} with config: {session_config.to_dict()}")
            return session_config
    
    def _create_session_config(self, session_id: int, config: Optional[Dict[str, Any]]) -> SessionConfig:
        """创建会话配置"""
        # 从默认配置开始
        config_dict = asdict(self.default_config)
        config_dict['session_id'] = session_id
        
        # 应用用户提供的配置
        if config:
            config_dict.update(config)
        
        return SessionConfig.from_dict(config_dict)
    
    def get_session_config(self, session_id: int) -> Optional[SessionConfig]:
        """获取会话配置"""
        with self.lock:
            return self.sessions.get(session_id)
    
    def update_session_config(self, session_id: int, config_updates: Dict[str, Any]) -> bool:
        """更新会话配置"""
        with self.lock:
            if session_id not in self.sessions:
                logger.error(f"Session {session_id} not found")
                return False
            
            session_config = self.sessions[session_id]
            
            # 更新配置
            for key, value in config_updates.items():
                if hasattr(session_config, key):
                    setattr(session_config, key, value)
                else:
                    logger.warning(f"Unknown config key: {key}")
            
            session_config.updated_at = datetime.now()
            
            logger.info(f"Updated session {session_id} config: {config_updates}")
            return True
    
    def remove_session(self, session_id: int) -> bool:
        """移除会话"""
        with self.lock:
            if session_id in self.sessions:
                del self.sessions[session_id]
                logger.info(f"Removed session {session_id}")
                return True
            return False
    
    def set_session_instance(self, session_id: int, instance: Any):
        """设置会话实例"""
        with self.lock:
            self.session_instances[session_id] = instance
    
    def get_session_instance(self, session_id: int) -> Optional[Any]:
        """获取会话实例"""
        with self.lock:
            return self.session_instances.get(session_id)
    
    def remove_session_instance(self, session_id: int):
        """移除会话实例"""
        with self.lock:
            if session_id in self.session_instances:
                del self.session_instances[session_id]
    
    def list_sessions(self) -> Dict[int, Dict[str, Any]]:
        """列出所有会话"""
        with self.lock:
            return {sid: config.to_dict() for sid, config in self.sessions.items()}
    
    def get_session_count(self) -> int:
        """获取会话数量"""
        with self.lock:
            return len(self.sessions)
    
    def set_default_config(self, config: Dict[str, Any]):
        """设置默认配置"""
        with self.lock:
            config_dict = asdict(self.default_config)
            config_dict.update(config)
            self.default_config = SessionConfig.from_dict(config_dict)
            logger.info(f"Updated default config: {config}")
    
    def cleanup_expired_sessions(self, max_age_hours: int = 24):
        """清理过期会话"""
        with self.lock:
            current_time = datetime.now()
            expired_sessions = []
            
            for session_id, config in self.sessions.items():
                age = current_time - config.updated_at
                if age.total_seconds() > max_age_hours * 3600:
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                self.remove_session(session_id)
                self.remove_session_instance(session_id)
                logger.info(f"Cleaned up expired session {session_id}")
            
            return len(expired_sessions)

# 全局会话管理器实例
session_manager = SessionManager()
