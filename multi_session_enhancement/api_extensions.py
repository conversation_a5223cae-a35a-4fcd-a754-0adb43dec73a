"""
API扩展 - 为多会话形象声音配置提供新的API接口
"""
import json
import asyncio
from aiohttp import web
from typing import Dict, Any, Optional
import logging

from .session_manager import session_manager, SessionConfig
from .resource_manager import resource_manager

logger = logging.getLogger(__name__)

class APIExtensions:
    """API扩展类"""
    
    def __init__(self, app: web.Application, build_nerfreal_func, nerfreals: Dict):
        self.app = app
        self.build_nerfreal_func = build_nerfreal_func
        self.nerfreals = nerfreals
        self._register_routes()
    
    def _register_routes(self):
        """注册新的API路由"""
        self.app.router.add_post("/offer_with_config", self.offer_with_config)
        self.app.router.add_post("/update_session_config", self.update_session_config)
        self.app.router.add_get("/session_config/{session_id}", self.get_session_config)
        self.app.router.add_get("/sessions", self.list_sessions)
        self.app.router.add_delete("/session/{session_id}", self.delete_session)
        self.app.router.add_get("/resource_stats", self.get_resource_stats)
        self.app.router.add_post("/preload_resources", self.preload_resources)
    
    async def offer_with_config(self, request):
        """创建带配置的会话"""
        try:
            params = await request.json()
            
            # 检查会话数量限制
            from app import opt  # 导入全局配置
            if len(self.nerfreals) >= opt.max_session:
                logger.info('reach max session')
                return web.Response(
                    content_type="application/json",
                    text=json.dumps({"code": -1, "msg": "reach max session"}),
                )
            
            # 生成会话ID
            from app import randN
            session_id = randN(6)
            
            # 获取会话配置
            session_config_data = params.get('session_config', {})
            session_config = session_manager.create_session(session_id, session_config_data)
            
            # 构建nerfreal实例
            self.nerfreals[session_id] = None
            nerfreal = await self._build_nerfreal_with_config(session_id, session_config)
            self.nerfreals[session_id] = nerfreal
            session_manager.set_session_instance(session_id, nerfreal)
            
            # 创建WebRTC连接
            from aiortc import RTCPeerConnection, RTCSessionDescription, RTCConfiguration, RTCIceServer
            from app import HumanPlayer, pcs
            
            ice_server = RTCIceServer(urls='stun:stun.miwifi.com:3478')
            pc = RTCPeerConnection(configuration=RTCConfiguration(iceServers=[ice_server]))
            pcs.add(pc)
            
            @pc.on("connectionstatechange")
            async def on_connectionstatechange():
                logger.info("Connection state is %s" % pc.connectionState)
                if pc.connectionState in ["failed", "closed"]:
                    await pc.close()
                    pcs.discard(pc)
                    if session_id in self.nerfreals:
                        del self.nerfreals[session_id]
                    session_manager.remove_session(session_id)
                    session_manager.remove_session_instance(session_id)
            
            player = HumanPlayer(self.nerfreals[session_id])
            pc.addTrack(player.audio)
            pc.addTrack(player.video)
            
            # 设置编解码器偏好
            from aiortc import RTCRtpSender
            capabilities = RTCRtpSender.getCapabilities("video")
            preferences = list(filter(lambda x: x.name == "H264", capabilities.codecs))
            preferences += list(filter(lambda x: x.name == "VP8", capabilities.codecs))
            preferences += list(filter(lambda x: x.name == "rtx", capabilities.codecs))
            transceiver = pc.getTransceivers()[1]
            transceiver.setCodecPreferences(preferences)
            
            # 处理offer
            offer = RTCSessionDescription(sdp=params["sdp"], type=params["type"])
            await pc.setRemoteDescription(offer)
            answer = await pc.createAnswer()
            await pc.setLocalDescription(answer)
            
            return web.Response(
                content_type="application/json",
                text=json.dumps({
                    "code": 0,
                    "sdp": pc.localDescription.sdp,
                    "type": pc.localDescription.type,
                    "session_id": session_id,
                    "session_config": session_config.to_dict()
                }),
            )
            
        except Exception as e:
            logger.exception('offer_with_config error:')
            return web.Response(
                content_type="application/json",
                text=json.dumps({"code": -1, "msg": str(e)}),
            )
    
    async def _build_nerfreal_with_config(self, session_id: int, config: SessionConfig):
        """根据配置构建nerfreal实例"""
        # 创建临时opt对象
        from types import SimpleNamespace
        temp_opt = SimpleNamespace()
        
        # 复制配置到temp_opt
        temp_opt.sessionid = session_id
        temp_opt.model = config.model
        temp_opt.avatar_id = config.avatar_id
        temp_opt.tts = config.tts
        temp_opt.REF_FILE = config.voice
        temp_opt.REF_TEXT = config.ref_text
        temp_opt.TTS_SERVER = config.tts_server
        temp_opt.batch_size = config.batch_size
        temp_opt.fps = config.fps
        temp_opt.W = config.W
        temp_opt.H = config.H
        temp_opt.customvideo_config = config.customvideo_config
        
        # 加载资源
        model = resource_manager.load_model(config.model)
        avatar = resource_manager.load_avatar(config.avatar_id)
        
        # 构建实例
        if config.model == 'wav2lip':
            from lipreal import LipReal
            nerfreal = LipReal(temp_opt, model, avatar)
        elif config.model == 'musetalk':
            from musereal import MuseReal
            nerfreal = MuseReal(temp_opt, model, avatar)
        elif config.model == 'ultralight':
            from lightreal import LightReal
            nerfreal = LightReal(temp_opt, model, avatar)
        else:
            raise ValueError(f"Unknown model type: {config.model}")
        
        return nerfreal
    
    async def update_session_config(self, request):
        """更新会话配置"""
        try:
            params = await request.json()
            session_id = params.get('session_id')
            config_updates = params.get('config', {})
            
            if not session_id:
                return web.Response(
                    content_type="application/json",
                    text=json.dumps({"code": -1, "msg": "session_id is required"}),
                )
            
            success = session_manager.update_session_config(session_id, config_updates)
            if success:
                updated_config = session_manager.get_session_config(session_id)
                return web.Response(
                    content_type="application/json",
                    text=json.dumps({
                        "code": 0,
                        "msg": "config updated",
                        "session_config": updated_config.to_dict()
                    }),
                )
            else:
                return web.Response(
                    content_type="application/json",
                    text=json.dumps({"code": -1, "msg": "session not found"}),
                )
                
        except Exception as e:
            logger.exception('update_session_config error:')
            return web.Response(
                content_type="application/json",
                text=json.dumps({"code": -1, "msg": str(e)}),
            )
    
    async def get_session_config(self, request):
        """获取会话配置"""
        try:
            session_id = int(request.match_info['session_id'])
            config = session_manager.get_session_config(session_id)
            
            if config:
                return web.Response(
                    content_type="application/json",
                    text=json.dumps({
                        "code": 0,
                        "session_config": config.to_dict()
                    }),
                )
            else:
                return web.Response(
                    content_type="application/json",
                    text=json.dumps({"code": -1, "msg": "session not found"}),
                )
                
        except Exception as e:
            logger.exception('get_session_config error:')
            return web.Response(
                content_type="application/json",
                text=json.dumps({"code": -1, "msg": str(e)}),
            )
    
    async def list_sessions(self, request):
        """列出所有会话"""
        try:
            sessions = session_manager.list_sessions()
            return web.Response(
                content_type="application/json",
                text=json.dumps({
                    "code": 0,
                    "sessions": sessions,
                    "count": len(sessions)
                }),
            )
        except Exception as e:
            logger.exception('list_sessions error:')
            return web.Response(
                content_type="application/json",
                text=json.dumps({"code": -1, "msg": str(e)}),
            )
    
    async def delete_session(self, request):
        """删除会话"""
        try:
            session_id = int(request.match_info['session_id'])
            
            # 移除nerfreal实例
            if session_id in self.nerfreals:
                del self.nerfreals[session_id]
            
            # 移除会话配置
            success = session_manager.remove_session(session_id)
            session_manager.remove_session_instance(session_id)
            
            return web.Response(
                content_type="application/json",
                text=json.dumps({
                    "code": 0 if success else -1,
                    "msg": "session deleted" if success else "session not found"
                }),
            )
        except Exception as e:
            logger.exception('delete_session error:')
            return web.Response(
                content_type="application/json",
                text=json.dumps({"code": -1, "msg": str(e)}),
            )
    
    async def get_resource_stats(self, request):
        """获取资源统计信息"""
        try:
            stats = resource_manager.get_cache_stats()
            return web.Response(
                content_type="application/json",
                text=json.dumps({"code": 0, "stats": stats}),
            )
        except Exception as e:
            logger.exception('get_resource_stats error:')
            return web.Response(
                content_type="application/json",
                text=json.dumps({"code": -1, "msg": str(e)}),
            )
    
    async def preload_resources(self, request):
        """预加载资源"""
        try:
            params = await request.json()
            configs = params.get('configs', [])
            
            # 在后台线程中预加载
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, resource_manager.preload_resources, configs)
            
            return web.Response(
                content_type="application/json",
                text=json.dumps({
                    "code": 0,
                    "msg": f"preloaded {len(configs)} configurations"
                }),
            )
        except Exception as e:
            logger.exception('preload_resources error:')
            return web.Response(
                content_type="application/json",
                text=json.dumps({"code": -1, "msg": str(e)}),
            )
