"""
动作API处理器 - 为LiveTalking提供动作管理的Web API接口
"""
import json
import logging
from typing import Dict, List, Optional, Any
from aiohttp import web
import asyncio
import os

from custom_action_manager import (
    CustomActionManager, 
    ActionConfig, 
    ActionType,
    get_action_manager,
    init_action_manager
)

logger = logging.getLogger(__name__)

class ActionAPIHandler:
    """动作API处理器"""
    
    def __init__(self, action_manager: CustomActionManager):
        self.action_manager = action_manager
    
    async def list_actions(self, request: web.Request) -> web.Response:
        """列出所有动作"""
        try:
            actions = self.action_manager.list_actions()
            
            # 转换为可序列化的格式
            actions_data = {}
            for audiotype, action in actions.items():
                actions_data[str(audiotype)] = {
                    "audiotype": action.audiotype,
                    "action_name": action.action_name,
                    "action_type": action.action_type.value,
                    "imgpath": action.imgpath,
                    "audiopath": action.audiopath,
                    "description": action.description,
                    "duration": action.duration,
                    "loop": action.loop,
                    "trigger_keywords": action.trigger_keywords,
                    "emotion_score": action.emotion_score,
                    "gesture_intensity": action.gesture_intensity,
                    "enabled": action.enabled
                }
            
            return web.json_response({
                "code": 0,
                "msg": "success",
                "data": {
                    "actions": actions_data,
                    "total": len(actions_data)
                }
            })
            
        except Exception as e:
            logger.error(f"列出动作失败: {e}")
            return web.json_response({
                "code": -1,
                "msg": f"列出动作失败: {str(e)}"
            })
    
    async def get_action(self, request: web.Request) -> web.Response:
        """获取特定动作"""
        try:
            audiotype = int(request.match_info['audiotype'])
            action = self.action_manager.get_action(audiotype)
            
            if not action:
                return web.json_response({
                    "code": -1,
                    "msg": f"动作 {audiotype} 不存在"
                })
            
            action_data = {
                "audiotype": action.audiotype,
                "action_name": action.action_name,
                "action_type": action.action_type.value,
                "imgpath": action.imgpath,
                "audiopath": action.audiopath,
                "description": action.description,
                "duration": action.duration,
                "loop": action.loop,
                "trigger_keywords": action.trigger_keywords,
                "emotion_score": action.emotion_score,
                "gesture_intensity": action.gesture_intensity,
                "enabled": action.enabled
            }
            
            return web.json_response({
                "code": 0,
                "msg": "success",
                "data": action_data
            })
            
        except ValueError:
            return web.json_response({
                "code": -1,
                "msg": "无效的动作类型ID"
            })
        except Exception as e:
            logger.error(f"获取动作失败: {e}")
            return web.json_response({
                "code": -1,
                "msg": f"获取动作失败: {str(e)}"
            })
    
    async def create_action(self, request: web.Request) -> web.Response:
        """创建新动作"""
        try:
            data = await request.json()
            
            # 验证必需字段
            required_fields = ['action_name', 'action_type', 'imgpath', 'audiopath']
            for field in required_fields:
                if field not in data:
                    return web.json_response({
                        "code": -1,
                        "msg": f"缺少必需字段: {field}"
                    })
            
            # 获取或生成audiotype
            audiotype = data.get('audiotype')
            if audiotype is None:
                audiotype = self.action_manager.get_next_available_audiotype()
            else:
                audiotype = int(audiotype)
            
            # 验证动作类型
            try:
                action_type = ActionType(data['action_type'])
            except ValueError:
                return web.json_response({
                    "code": -1,
                    "msg": f"无效的动作类型: {data['action_type']}"
                })
            
            # 创建动作配置
            action_config = ActionConfig(
                audiotype=audiotype,
                action_name=data['action_name'],
                action_type=action_type,
                imgpath=data['imgpath'],
                audiopath=data['audiopath'],
                description=data.get('description', ''),
                duration=data.get('duration', 0.0),
                loop=data.get('loop', True),
                trigger_keywords=data.get('trigger_keywords', []),
                emotion_score=data.get('emotion_score', 0.0),
                gesture_intensity=data.get('gesture_intensity', 1.0),
                enabled=data.get('enabled', True)
            )
            
            # 添加动作
            success = self.action_manager.add_action(action_config)
            
            if success:
                return web.json_response({
                    "code": 0,
                    "msg": "动作创建成功",
                    "data": {"audiotype": audiotype}
                })
            else:
                return web.json_response({
                    "code": -1,
                    "msg": "动作创建失败"
                })
                
        except Exception as e:
            logger.error(f"创建动作失败: {e}")
            return web.json_response({
                "code": -1,
                "msg": f"创建动作失败: {str(e)}"
            })
    
    async def update_action(self, request: web.Request) -> web.Response:
        """更新动作"""
        try:
            audiotype = int(request.match_info['audiotype'])
            data = await request.json()
            
            # 获取现有动作
            existing_action = self.action_manager.get_action(audiotype)
            if not existing_action:
                return web.json_response({
                    "code": -1,
                    "msg": f"动作 {audiotype} 不存在"
                })
            
            # 更新字段
            updated_fields = {}
            
            if 'action_name' in data:
                updated_fields['action_name'] = data['action_name']
            
            if 'action_type' in data:
                try:
                    updated_fields['action_type'] = ActionType(data['action_type'])
                except ValueError:
                    return web.json_response({
                        "code": -1,
                        "msg": f"无效的动作类型: {data['action_type']}"
                    })
            
            if 'imgpath' in data:
                updated_fields['imgpath'] = data['imgpath']
            
            if 'audiopath' in data:
                updated_fields['audiopath'] = data['audiopath']
            
            if 'description' in data:
                updated_fields['description'] = data['description']
            
            if 'duration' in data:
                updated_fields['duration'] = float(data['duration'])
            
            if 'loop' in data:
                updated_fields['loop'] = bool(data['loop'])
            
            if 'trigger_keywords' in data:
                updated_fields['trigger_keywords'] = data['trigger_keywords']
            
            if 'emotion_score' in data:
                updated_fields['emotion_score'] = float(data['emotion_score'])
            
            if 'gesture_intensity' in data:
                updated_fields['gesture_intensity'] = float(data['gesture_intensity'])
            
            if 'enabled' in data:
                updated_fields['enabled'] = bool(data['enabled'])
            
            # 创建更新后的动作配置
            updated_action = ActionConfig(
                audiotype=existing_action.audiotype,
                action_name=updated_fields.get('action_name', existing_action.action_name),
                action_type=updated_fields.get('action_type', existing_action.action_type),
                imgpath=updated_fields.get('imgpath', existing_action.imgpath),
                audiopath=updated_fields.get('audiopath', existing_action.audiopath),
                description=updated_fields.get('description', existing_action.description),
                duration=updated_fields.get('duration', existing_action.duration),
                loop=updated_fields.get('loop', existing_action.loop),
                trigger_keywords=updated_fields.get('trigger_keywords', existing_action.trigger_keywords),
                emotion_score=updated_fields.get('emotion_score', existing_action.emotion_score),
                gesture_intensity=updated_fields.get('gesture_intensity', existing_action.gesture_intensity),
                enabled=updated_fields.get('enabled', existing_action.enabled)
            )
            
            # 更新动作
            success = self.action_manager.add_action(updated_action)  # add_action会覆盖现有动作
            
            if success:
                return web.json_response({
                    "code": 0,
                    "msg": "动作更新成功"
                })
            else:
                return web.json_response({
                    "code": -1,
                    "msg": "动作更新失败"
                })
                
        except ValueError:
            return web.json_response({
                "code": -1,
                "msg": "无效的动作类型ID"
            })
        except Exception as e:
            logger.error(f"更新动作失败: {e}")
            return web.json_response({
                "code": -1,
                "msg": f"更新动作失败: {str(e)}"
            })
    
    async def delete_action(self, request: web.Request) -> web.Response:
        """删除动作"""
        try:
            audiotype = int(request.match_info['audiotype'])
            
            success = self.action_manager.remove_action(audiotype)
            
            if success:
                return web.json_response({
                    "code": 0,
                    "msg": "动作删除成功"
                })
            else:
                return web.json_response({
                    "code": -1,
                    "msg": "动作删除失败"
                })
                
        except ValueError:
            return web.json_response({
                "code": -1,
                "msg": "无效的动作类型ID"
            })
        except Exception as e:
            logger.error(f"删除动作失败: {e}")
            return web.json_response({
                "code": -1,
                "msg": f"删除动作失败: {str(e)}"
            })
    
    async def search_actions(self, request: web.Request) -> web.Response:
        """搜索动作"""
        try:
            # 支持GET和POST请求
            if request.method == 'GET':
                query_params = request.query
                search_data = dict(query_params)
            else:
                search_data = await request.json()

            # 按关键词搜索
            if 'keyword' in search_data:
                keyword = search_data['keyword']
                actions = self.action_manager.find_actions_by_keyword(keyword)

            # 按动作类型搜索
            elif 'action_type' in search_data:
                try:
                    action_type = ActionType(search_data['action_type'])
                    actions = self.action_manager.find_actions_by_type(action_type)
                except ValueError:
                    return web.json_response({
                        "code": -1,
                        "msg": f"无效的动作类型: {search_data['action_type']}"
                    })

            # 按情感分数搜索
            elif 'emotion_min' in search_data or 'emotion_max' in search_data:
                min_score = float(search_data.get('emotion_min', -1.0))
                max_score = float(search_data.get('emotion_max', 1.0))
                actions = self.action_manager.find_actions_by_emotion(min_score, max_score)

            else:
                return web.json_response({
                    "code": -1,
                    "msg": "请提供搜索参数: keyword, action_type, 或 emotion_min/emotion_max"
                })

            # 转换结果
            results = []
            for action in actions:
                results.append({
                    "audiotype": action.audiotype,
                    "action_name": action.action_name,
                    "action_type": action.action_type.value,
                    "description": action.description,
                    "trigger_keywords": action.trigger_keywords,
                    "emotion_score": action.emotion_score,
                    "enabled": action.enabled
                })

            return web.json_response({
                "code": 0,
                "msg": "success",
                "data": {
                    "results": results,
                    "total": len(results)
                }
            })

        except Exception as e:
            logger.error(f"搜索动作失败: {e}")
            return web.json_response({
                "code": -1,
                "msg": f"搜索动作失败: {str(e)}"
            })
    
    async def validate_actions(self, request: web.Request) -> web.Response:
        """验证所有动作的完整性"""
        try:
            validation_results = self.action_manager.validate_all_actions()
            
            return web.json_response({
                "code": 0,
                "msg": "success",
                "data": {
                    "validation_results": validation_results,
                    "total_issues": len(validation_results),
                    "valid_actions": len(self.action_manager.list_actions()) - len(validation_results)
                }
            })
            
        except Exception as e:
            logger.error(f"验证动作失败: {e}")
            return web.json_response({
                "code": -1,
                "msg": f"验证动作失败: {str(e)}"
            })
    
    async def export_legacy_config(self, request: web.Request) -> web.Response:
        """导出为旧格式配置"""
        try:
            legacy_format = self.action_manager.export_legacy_format()
            
            return web.json_response({
                "code": 0,
                "msg": "success",
                "data": {
                    "legacy_config": legacy_format,
                    "format": "legacy_livetalking"
                }
            })
            
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return web.json_response({
                "code": -1,
                "msg": f"导出配置失败: {str(e)}"
            })
    
    async def get_action_types(self, request: web.Request) -> web.Response:
        """获取所有支持的动作类型"""
        try:
            action_types = []
            for action_type in ActionType:
                action_types.append({
                    "value": action_type.value,
                    "name": action_type.name,
                    "description": self._get_action_type_description(action_type)
                })
            
            return web.json_response({
                "code": 0,
                "msg": "success",
                "data": {
                    "action_types": action_types,
                    "total": len(action_types)
                }
            })
            
        except Exception as e:
            logger.error(f"获取动作类型失败: {e}")
            return web.json_response({
                "code": -1,
                "msg": f"获取动作类型失败: {str(e)}"
            })
    
    def _get_action_type_description(self, action_type: ActionType) -> str:
        """获取动作类型描述"""
        descriptions = {
            ActionType.IDLE: "静止/待机状态",
            ActionType.GREETING: "打招呼手势",
            ActionType.NODDING: "点头动作",
            ActionType.SHAKING_HEAD: "摇头动作",
            ActionType.WAVING: "挥手动作",
            ActionType.POINTING: "指向手势",
            ActionType.THUMBS_UP: "点赞手势",
            ActionType.THINKING: "思考动作",
            ActionType.EXPLAINING: "解释说明手势",
            ActionType.SURPRISED: "惊讶表情",
            ActionType.HAPPY: "开心表情",
            ActionType.SAD: "悲伤表情",
            ActionType.ANGRY: "生气表情",
            ActionType.CONFUSED: "困惑表情",
            ActionType.APPLAUDING: "鼓掌动作",
            ActionType.PRESENTING: "展示手势",
            ActionType.CUSTOM: "自定义动作"
        }
        return descriptions.get(action_type, "未知动作类型")

def setup_action_routes(app: web.Application, action_manager: CustomActionManager):
    """设置动作管理的路由"""
    handler = ActionAPIHandler(action_manager)

    # 动作管理路由
    app.router.add_get('/api/actions', handler.list_actions)
    app.router.add_get('/api/actions/{audiotype}', handler.get_action)
    app.router.add_post('/api/actions', handler.create_action)
    app.router.add_put('/api/actions/{audiotype}', handler.update_action)
    app.router.add_delete('/api/actions/{audiotype}', handler.delete_action)

    # 搜索和查询路由 (支持GET和POST)
    app.router.add_get('/api/actions/search', handler.search_actions)
    app.router.add_post('/api/actions/search', handler.search_actions)
    app.router.add_get('/api/actions/validate', handler.validate_actions)
    app.router.add_get('/api/actions/types', handler.get_action_types)

    # 导出路由
    app.router.add_get('/api/actions/export/legacy', handler.export_legacy_config)

    logger.info("动作管理API路由设置完成")

# 兼容原有的set_audiotype接口
async def enhanced_set_audiotype(request: web.Request, nerfreals: Dict) -> web.Response:
    """增强的set_audiotype接口，支持智能动作选择"""
    try:
        params = await request.json()
        sessionid = params.get('sessionid', 0)
        
        # 获取动作管理器
        action_manager = get_action_manager()
        if not action_manager:
            # 回退到原有逻辑
            audiotype = params.get('audiotype', 0)
            reinit = params.get('reinit', True)
            
            if sessionid in nerfreals:
                nerfreals[sessionid].set_custom_state(audiotype, reinit)
            
            return web.json_response({
                "code": 0,
                "msg": "ok (legacy mode)"
            })
        
        # 智能动作选择
        if 'keyword' in params:
            # 根据关键词选择动作
            keyword = params['keyword']
            actions = action_manager.find_actions_by_keyword(keyword)
            
            if actions:
                # 选择最匹配的动作
                best_action = actions[0]  # 可以添加更复杂的选择逻辑
                audiotype = best_action.audiotype
            else:
                audiotype = params.get('audiotype', 0)
        else:
            audiotype = params.get('audiotype', 0)
        
        reinit = params.get('reinit', True)
        
        # 设置动作状态
        if sessionid in nerfreals:
            nerfreals[sessionid].set_custom_state(audiotype, reinit)
        
        return web.json_response({
            "code": 0,
            "msg": "ok",
            "data": {"audiotype": audiotype}
        })
        
    except Exception as e:
        logger.error(f"设置动作类型失败: {e}")
        return web.json_response({
            "code": -1,
            "msg": str(e)
        })
