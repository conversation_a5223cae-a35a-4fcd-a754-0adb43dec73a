"""
ASR配置管理器 - 支持多种ASR服务
包括FunASR、OpenAI Whisper API、SenseVoice等
"""
import os
import json
import logging
import asyncio
import aiohttp
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class ASRProvider(Enum):
    """ASR服务提供商"""
    FUNASR = "funasr"
    OPENAI_WHISPER = "openai_whisper"
    SENSEVOICE = "sensevoice"
    AZURE_SPEECH = "azure_speech"
    TENCENT_ASR = "tencent_asr"
    BAIDU_ASR = "baidu_asr"

@dataclass
class ASRConfig:
    """ASR配置类"""
    provider: str = "funasr"
    enabled: bool = True
    
    # 通用配置
    language: str = "zh"
    sample_rate: int = 16000
    format: str = "pcm"
    
    # FunASR配置
    funasr_websocket_url: str = "wss://www.funasr.com:10096/"
    funasr_mode: str = "2pass"  # 2pass, online, offline
    funasr_use_itn: bool = True
    funasr_hotwords: Optional[str] = None
    
    # OpenAI Whisper API配置
    openai_api_key: str = ""
    openai_base_url: str = "https://api.openai.com/v1"
    openai_model: str = "whisper-1"
    openai_temperature: float = 0.0
    openai_language: str = "zh"
    
    # SenseVoice配置
    sensevoice_api_url: str = "http://localhost:8000"
    sensevoice_model: str = "sensevoice-small"
    sensevoice_language: str = "auto"
    sensevoice_use_itn: bool = True
    
    # Azure Speech配置
    azure_speech_key: str = ""
    azure_speech_region: str = ""
    azure_speech_language: str = "zh-CN"
    
    # 腾讯云ASR配置
    tencent_secret_id: str = ""
    tencent_secret_key: str = ""
    tencent_app_id: str = ""
    tencent_engine_model_type: str = "16k_zh"
    
    # 百度ASR配置
    baidu_app_id: str = ""
    baidu_api_key: str = ""
    baidu_secret_key: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ASRConfig':
        """从字典创建实例"""
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

class OpenAIWhisperASR:
    """OpenAI Whisper API ASR服务"""
    
    def __init__(self, config: ASRConfig):
        self.config = config
        self.session = None
    
    async def initialize(self):
        """初始化HTTP会话"""
        if not self.session:
            self.session = aiohttp.ClientSession()
    
    async def transcribe_audio(self, audio_data: bytes, format: str = "wav") -> str:
        """转录音频"""
        if not self.session:
            await self.initialize()
        
        try:
            # 准备请求数据
            data = aiohttp.FormData()
            data.add_field('file', audio_data, filename=f'audio.{format}', content_type=f'audio/{format}')
            data.add_field('model', self.config.openai_model)
            data.add_field('language', self.config.openai_language)
            data.add_field('temperature', str(self.config.openai_temperature))
            data.add_field('response_format', 'json')
            
            headers = {
                'Authorization': f'Bearer {self.config.openai_api_key}'
            }
            
            url = f"{self.config.openai_base_url.rstrip('/')}/audio/transcriptions"
            
            async with self.session.post(url, data=data, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('text', '')
                else:
                    error_text = await response.text()
                    logger.error(f"OpenAI Whisper API错误: {response.status} - {error_text}")
                    return ""
                    
        except Exception as e:
            logger.error(f"OpenAI Whisper转录失败: {e}")
            return ""
    
    async def close(self):
        """关闭会话"""
        if self.session:
            await self.session.close()
            self.session = None

class SenseVoiceASR:
    """SenseVoice ASR服务"""
    
    def __init__(self, config: ASRConfig):
        self.config = config
        self.session = None
    
    async def initialize(self):
        """初始化HTTP会话"""
        if not self.session:
            self.session = aiohttp.ClientSession()
    
    async def transcribe_audio(self, audio_data: bytes, format: str = "wav") -> str:
        """转录音频"""
        if not self.session:
            await self.initialize()
        
        try:
            # SenseVoice API请求格式
            data = aiohttp.FormData()
            data.add_field('audio', audio_data, filename=f'audio.{format}', content_type=f'audio/{format}')
            data.add_field('model', self.config.sensevoice_model)
            data.add_field('language', self.config.sensevoice_language)
            data.add_field('use_itn', str(self.config.sensevoice_use_itn).lower())
            
            url = f"{self.config.sensevoice_api_url.rstrip('/')}/v1/audio/transcriptions"
            
            async with self.session.post(url, data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    # 根据SenseVoice的响应格式调整
                    return result.get('text', '') or result.get('transcription', '')
                else:
                    error_text = await response.text()
                    logger.error(f"SenseVoice API错误: {response.status} - {error_text}")
                    return ""
                    
        except Exception as e:
            logger.error(f"SenseVoice转录失败: {e}")
            return ""
    
    async def close(self):
        """关闭会话"""
        if self.session:
            await self.session.close()
            self.session = None

class ASRManager:
    """ASR管理器 - 统一管理多种ASR服务"""
    
    def __init__(self, config: ASRConfig):
        self.config = config
        self.current_asr = None
        self._initialize_asr()
    
    def _initialize_asr(self):
        """初始化ASR服务"""
        provider = ASRProvider(self.config.provider)
        
        if provider == ASRProvider.OPENAI_WHISPER:
            self.current_asr = OpenAIWhisperASR(self.config)
            logger.info("初始化OpenAI Whisper ASR服务")
        elif provider == ASRProvider.SENSEVOICE:
            self.current_asr = SenseVoiceASR(self.config)
            logger.info("初始化SenseVoice ASR服务")
        elif provider == ASRProvider.FUNASR:
            # FunASR使用WebSocket，保持原有实现
            logger.info("使用FunASR WebSocket服务")
        else:
            logger.warning(f"不支持的ASR提供商: {provider}")
    
    async def transcribe_audio(self, audio_data: bytes, format: str = "wav") -> str:
        """转录音频"""
        if not self.config.enabled:
            return ""
        
        provider = ASRProvider(self.config.provider)
        
        if provider == ASRProvider.FUNASR:
            # FunASR使用WebSocket实时流式处理，这里返回空
            # 实际处理在web/asr/目录下的JavaScript代码中
            return ""
        elif self.current_asr:
            return await self.current_asr.transcribe_audio(audio_data, format)
        else:
            logger.error("ASR服务未初始化")
            return ""
    
    async def close(self):
        """关闭ASR服务"""
        if self.current_asr and hasattr(self.current_asr, 'close'):
            await self.current_asr.close()

class ASRConfigManager:
    """ASR配置管理器"""
    
    def __init__(self, config_file: str = "asr_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self.asr_manager = ASRManager(self.config)
    
    def _load_config(self) -> ASRConfig:
        """加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return ASRConfig.from_dict(data)
            except Exception as e:
                logger.error(f"加载ASR配置失败: {e}")
        
        # 返回默认配置
        return ASRConfig()
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config.to_dict(), f, ensure_ascii=False, indent=2)
            logger.info(f"ASR配置已保存到: {self.config_file}")
        except Exception as e:
            logger.error(f"保存ASR配置失败: {e}")
    
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        
        # 重新初始化ASR服务
        self.asr_manager = ASRManager(self.config)
        self.save_config()
    
    def get_supported_providers(self) -> List[Dict[str, str]]:
        """获取支持的ASR提供商"""
        return [
            {"value": "funasr", "label": "FunASR (阿里巴巴)", "type": "websocket"},
            {"value": "openai_whisper", "label": "OpenAI Whisper API", "type": "http"},
            {"value": "sensevoice", "label": "SenseVoice", "type": "http"},
            {"value": "azure_speech", "label": "Azure Speech", "type": "http"},
            {"value": "tencent_asr", "label": "腾讯云ASR", "type": "http"},
            {"value": "baidu_asr", "label": "百度ASR", "type": "http"}
        ]
    
    async def transcribe_audio(self, audio_data: bytes, format: str = "wav") -> str:
        """转录音频"""
        return await self.asr_manager.transcribe_audio(audio_data, format)
    
    async def close(self):
        """关闭ASR服务"""
        await self.asr_manager.close()

# 全局ASR配置管理器
_global_asr_config_manager: Optional[ASRConfigManager] = None

def init_asr_config_manager(config_file: str = "asr_config.json") -> ASRConfigManager:
    """初始化全局ASR配置管理器"""
    global _global_asr_config_manager
    _global_asr_config_manager = ASRConfigManager(config_file)
    return _global_asr_config_manager

def get_asr_config_manager() -> Optional[ASRConfigManager]:
    """获取全局ASR配置管理器"""
    return _global_asr_config_manager

def get_asr_config() -> ASRConfig:
    """获取ASR配置"""
    manager = get_asr_config_manager()
    if manager:
        return manager.config
    else:
        return ASRConfig()

async def transcribe_audio(audio_data: bytes, format: str = "wav") -> str:
    """转录音频 - 全局函数"""
    manager = get_asr_config_manager()
    if manager:
        return await manager.transcribe_audio(audio_data, format)
    else:
        logger.error("ASR配置管理器未初始化")
        return ""
