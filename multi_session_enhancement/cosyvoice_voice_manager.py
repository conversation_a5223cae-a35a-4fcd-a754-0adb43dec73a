"""
CosyVoice音色管理器 - 优化GPUStack部署的音色克隆性能
"""
import os
import json
import hashlib
import logging
import aiohttp
import asyncio
from typing import Dict, Optional, List, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import requests

logger = logging.getLogger(__name__)

@dataclass
class VoiceProfile:
    """音色配置文件"""
    voice_id: str                    # 音色ID
    name: str                       # 音色名称
    description: str = ""           # 音色描述
    audio_path: str = ""            # 本地音频文件路径
    ref_text: str = ""              # 参考文本
    server_voice_id: str = ""       # 服务器端音色ID (如果支持)
    audio_hash: str = ""            # 音频文件哈希值
    created_at: datetime = None     # 创建时间
    last_used: datetime = None      # 最后使用时间
    use_count: int = 0              # 使用次数
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.last_used is None:
            self.last_used = datetime.now()

class CosyVoiceManager:
    """CosyVoice音色管理器"""
    
    def __init__(self, server_url: str = "http://localhost:50000", config_file: str = "data/voice_profiles.json"):
        self.server_url = server_url.rstrip('/')
        self.config_file = config_file
        self.voice_profiles: Dict[str, VoiceProfile] = {}
        self.session = None
        self._load_voice_profiles()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def _load_voice_profiles(self):
        """加载音色配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for voice_id, profile_data in data.items():
                        # 转换datetime字符串
                        if 'created_at' in profile_data and isinstance(profile_data['created_at'], str):
                            profile_data['created_at'] = datetime.fromisoformat(profile_data['created_at'])
                        if 'last_used' in profile_data and isinstance(profile_data['last_used'], str):
                            profile_data['last_used'] = datetime.fromisoformat(profile_data['last_used'])
                        
                        self.voice_profiles[voice_id] = VoiceProfile(**profile_data)
                logger.info(f"加载了 {len(self.voice_profiles)} 个音色配置")
        except Exception as e:
            logger.error(f"加载音色配置失败: {e}")
            self.voice_profiles = {}
    
    def _save_voice_profiles(self):
        """保存音色配置文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            # 转换为可序列化的格式
            data = {}
            for voice_id, profile in self.voice_profiles.items():
                profile_dict = asdict(profile)
                # 转换datetime为字符串
                if profile_dict['created_at']:
                    profile_dict['created_at'] = profile_dict['created_at'].isoformat()
                if profile_dict['last_used']:
                    profile_dict['last_used'] = profile_dict['last_used'].isoformat()
                data[voice_id] = profile_dict
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"保存了 {len(self.voice_profiles)} 个音色配置")
        except Exception as e:
            logger.error(f"保存音色配置失败: {e}")
    
    def _calculate_audio_hash(self, audio_path: str) -> str:
        """计算音频文件哈希值"""
        try:
            with open(audio_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception as e:
            logger.error(f"计算音频哈希失败: {e}")
            return ""
    
    async def register_voice(self, voice_id: str, name: str, audio_path: str, ref_text: str, description: str = "") -> bool:
        """注册新音色"""
        try:
            # 验证音频文件存在
            if not os.path.exists(audio_path):
                logger.error(f"音频文件不存在: {audio_path}")
                return False
            
            # 计算音频哈希
            audio_hash = self._calculate_audio_hash(audio_path)
            
            # 检查是否已存在相同哈希的音色
            for existing_id, existing_profile in self.voice_profiles.items():
                if existing_profile.audio_hash == audio_hash and existing_id != voice_id:
                    logger.warning(f"音色 {voice_id} 与现有音色 {existing_id} 使用相同音频文件")
            
            # 创建音色配置
            voice_profile = VoiceProfile(
                voice_id=voice_id,
                name=name,
                description=description,
                audio_path=audio_path,
                ref_text=ref_text,
                audio_hash=audio_hash
            )
            
            # 尝试在服务器端预注册音色 (如果服务器支持)
            server_voice_id = await self._register_voice_on_server(voice_profile)
            if server_voice_id:
                voice_profile.server_voice_id = server_voice_id
                logger.info(f"音色 {voice_id} 已在服务器端注册，ID: {server_voice_id}")
            
            # 保存到本地
            self.voice_profiles[voice_id] = voice_profile
            self._save_voice_profiles()
            
            logger.info(f"音色 {voice_id} 注册成功")
            return True
            
        except Exception as e:
            logger.error(f"注册音色失败: {e}")
            return False
    
    async def _register_voice_on_server(self, voice_profile: VoiceProfile) -> Optional[str]:
        """在服务器端预注册音色 (如果支持)"""
        try:
            # 检查服务器是否支持音色预注册
            if not await self._check_server_voice_registration_support():
                return None
            
            # 上传音色到服务器 (具体API根据GPUStack CosyVoice实现调整)
            data = aiohttp.FormData()
            data.add_field('voice_name', voice_profile.name)
            data.add_field('ref_text', voice_profile.ref_text)
            
            with open(voice_profile.audio_path, 'rb') as f:
                data.add_field('audio_file', f, filename=os.path.basename(voice_profile.audio_path))
            
            url = f"{self.server_url}/register_voice"  # 假设的API端点
            
            if self.session:
                async with self.session.post(url, data=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('voice_id')
            else:
                # 同步请求作为备选
                files = {'audio_file': open(voice_profile.audio_path, 'rb')}
                data_dict = {'voice_name': voice_profile.name, 'ref_text': voice_profile.ref_text}
                response = requests.post(url, files=files, data=data_dict)
                if response.status_code == 200:
                    return response.json().get('voice_id')
            
        except Exception as e:
            logger.debug(f"服务器端音色注册失败 (可能不支持): {e}")
        
        return None
    
    async def _check_server_voice_registration_support(self) -> bool:
        """检查服务器是否支持音色预注册"""
        try:
            url = f"{self.server_url}/capabilities"  # 假设的能力查询端点
            
            if self.session:
                async with self.session.get(url) as response:
                    if response.status == 200:
                        capabilities = await response.json()
                        return capabilities.get('voice_registration', False)
            else:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    capabilities = response.json()
                    return capabilities.get('voice_registration', False)
                    
        except Exception as e:
            logger.debug(f"检查服务器能力失败: {e}")
        
        return False
    
    async def synthesize_with_voice(self, text: str, voice_id: str) -> Optional[bytes]:
        """使用指定音色合成语音"""
        try:
            if voice_id not in self.voice_profiles:
                logger.error(f"音色 {voice_id} 不存在")
                return None
            
            voice_profile = self.voice_profiles[voice_id]
            
            # 更新使用统计
            voice_profile.last_used = datetime.now()
            voice_profile.use_count += 1
            self._save_voice_profiles()
            
            # 优先使用服务器端预注册的音色
            if voice_profile.server_voice_id:
                return await self._synthesize_with_server_voice(text, voice_profile)
            else:
                return await self._synthesize_with_file_upload(text, voice_profile)
                
        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return None
    
    async def _synthesize_with_server_voice(self, text: str, voice_profile: VoiceProfile) -> Optional[bytes]:
        """使用服务器端预注册音色合成"""
        try:
            payload = {
                'tts_text': text,
                'voice_id': voice_profile.server_voice_id  # 使用服务器端音色ID
            }
            
            url = f"{self.server_url}/inference_with_voice_id"  # 假设的API端点
            
            if self.session:
                async with self.session.post(url, json=payload) as response:
                    if response.status == 200:
                        return await response.read()
            else:
                response = requests.post(url, json=payload, stream=True)
                if response.status_code == 200:
                    return response.content
                    
        except Exception as e:
            logger.error(f"使用服务器音色合成失败: {e}")
        
        return None
    
    async def _synthesize_with_file_upload(self, text: str, voice_profile: VoiceProfile) -> Optional[bytes]:
        """使用文件上传方式合成 (回退方案)"""
        try:
            payload = {
                'tts_text': text,
                'prompt_text': voice_profile.ref_text
            }
            
            files = [('prompt_wav', ('prompt_wav', open(voice_profile.audio_path, 'rb'), 'application/octet-stream'))]
            
            url = f"{self.server_url}/inference_zero_shot"
            
            if self.session:
                data = aiohttp.FormData()
                data.add_field('tts_text', text)
                data.add_field('prompt_text', voice_profile.ref_text)
                
                with open(voice_profile.audio_path, 'rb') as f:
                    data.add_field('prompt_wav', f, filename='prompt_wav')
                
                async with self.session.post(url, data=data) as response:
                    if response.status == 200:
                        return await response.read()
            else:
                response = requests.post(url, data=payload, files=files, stream=True)
                if response.status_code == 200:
                    return response.content
                    
        except Exception as e:
            logger.error(f"文件上传合成失败: {e}")
        
        return None
    
    def list_voices(self) -> List[Dict]:
        """列出所有音色"""
        voices = []
        for voice_id, profile in self.voice_profiles.items():
            voices.append({
                'voice_id': voice_id,
                'name': profile.name,
                'description': profile.description,
                'use_count': profile.use_count,
                'last_used': profile.last_used.isoformat() if profile.last_used else None,
                'has_server_registration': bool(profile.server_voice_id)
            })
        return voices
    
    def remove_voice(self, voice_id: str) -> bool:
        """删除音色"""
        if voice_id in self.voice_profiles:
            del self.voice_profiles[voice_id]
            self._save_voice_profiles()
            logger.info(f"音色 {voice_id} 已删除")
            return True
        return False
    
    def get_voice_info(self, voice_id: str) -> Optional[Dict]:
        """获取音色信息"""
        if voice_id in self.voice_profiles:
            profile = self.voice_profiles[voice_id]
            return {
                'voice_id': voice_id,
                'name': profile.name,
                'description': profile.description,
                'audio_path': profile.audio_path,
                'ref_text': profile.ref_text,
                'audio_hash': profile.audio_hash,
                'server_voice_id': profile.server_voice_id,
                'use_count': profile.use_count,
                'created_at': profile.created_at.isoformat() if profile.created_at else None,
                'last_used': profile.last_used.isoformat() if profile.last_used else None
            }
        return None

# 全局音色管理器实例
_voice_manager = None

def get_voice_manager(server_url: str = "http://localhost:50000") -> CosyVoiceManager:
    """获取全局音色管理器实例"""
    global _voice_manager
    if _voice_manager is None:
        _voice_manager = CosyVoiceManager(server_url)
    return _voice_manager

async def init_voice_manager(server_url: str = "http://localhost:50000") -> CosyVoiceManager:
    """初始化异步音色管理器"""
    manager = CosyVoiceManager(server_url)
    await manager.__aenter__()
    return manager
