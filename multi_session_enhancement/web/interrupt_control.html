<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能打断控制面板</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .control-group input, .control-group select, .control-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .control-group textarea {
            height: 80px;
            resize: vertical;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #51cf66, #40c057);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffd43b, #fab005);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-panel {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .status-panel.error {
            background: #ffebee;
            border-color: #f44336;
        }
        
        .log-panel {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .keyword-tag {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 2px;
            font-size: 12px;
        }
        
        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .slider {
            flex: 1;
        }
        
        .slider-value {
            min-width: 60px;
            text-align: center;
            font-weight: bold;
            color: #667eea;
        }
        
        .connection-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .connection-status.connected {
            background: #4caf50;
        }
        
        .connection-status.disconnected {
            background: #f44336;
        }
        
        @media (max-width: 600px) {
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 智能打断控制面板</h1>
            <p>实时控制数字人语音打断功能</p>
        </div>
        
        <div class="content">
            <!-- 连接状态 -->
            <div class="section">
                <h3>🔗 连接状态</h3>
                <div>
                    <span class="connection-status disconnected" id="connectionStatus"></span>
                    <span id="connectionText">未连接</span>
                    <div class="button-group" style="margin-top: 10px;">
                        <button class="btn btn-primary" onclick="connectWebSocket()">连接</button>
                        <button class="btn btn-danger" onclick="disconnectWebSocket()">断开</button>
                    </div>
                </div>
            </div>
            
            <!-- 快速打断 -->
            <div class="section">
                <h3>⚡ 快速打断</h3>
                <div class="button-group">
                    <button class="btn btn-danger" onclick="manualInterrupt()">🛑 立即打断</button>
                    <button class="btn btn-warning" onclick="testKeyword('停')">🗣️ 测试"停"</button>
                    <button class="btn btn-warning" onclick="testKeyword('够了')">🗣️ 测试"够了"</button>
                    <button class="btn btn-warning" onclick="testKeyword('等等')">🗣️ 测试"等等"</button>
                </div>
            </div>
            
            <!-- 文本打断测试 -->
            <div class="section">
                <h3>💬 文本打断测试</h3>
                <div class="control-group">
                    <label for="testText">输入测试文本:</label>
                    <textarea id="testText" placeholder="输入包含打断关键词的文本，例如：'停止说话'、'够了'、'等等'等"></textarea>
                </div>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="testTextInterrupt()">🧪 测试文本打断</button>
                    <button class="btn btn-success" onclick="clearTestText()">🗑️ 清空</button>
                </div>
            </div>
            
            <!-- 打断配置 -->
            <div class="section">
                <h3>⚙️ 打断配置</h3>
                
                <div class="control-group">
                    <label>关键词敏感度:</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="keywordSensitivity" min="0" max="1" step="0.1" value="0.8" onchange="updateSliderValue('keywordSensitivity')">
                        <span class="slider-value" id="keywordSensitivityValue">0.8</span>
                    </div>
                </div>
                
                <div class="control-group">
                    <label>语音活动阈值:</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="voiceThreshold" min="0" max="1" step="0.1" value="0.5" onchange="updateSliderValue('voiceThreshold')">
                        <span class="slider-value" id="voiceThresholdValue">0.5</span>
                    </div>
                </div>
                
                <div class="control-group">
                    <label>最大说话时长 (秒):</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="maxDuration" min="5" max="60" step="5" value="30" onchange="updateSliderValue('maxDuration')">
                        <span class="slider-value" id="maxDurationValue">30</span>
                    </div>
                </div>
                
                <div class="control-group">
                    <label>打断延迟 (秒):</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="interruptDelay" min="0" max="2" step="0.1" value="0.5" onchange="updateSliderValue('interruptDelay')">
                        <span class="slider-value" id="interruptDelayValue">0.5</span>
                    </div>
                </div>
                
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="smartInterrupt" checked> 启用智能打断
                    </label>
                </div>
                
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="sentenceBoundary" checked> 句子边界检测
                    </label>
                </div>
                
                <div class="button-group">
                    <button class="btn btn-success" onclick="updateConfig()">💾 保存配置</button>
                    <button class="btn btn-primary" onclick="loadConfig()">📥 加载配置</button>
                </div>
            </div>
            
            <!-- 会话状态 -->
            <div class="section">
                <h3>📊 会话状态</h3>
                <div class="control-group">
                    <label for="sessionId">会话ID:</label>
                    <input type="text" id="sessionId" value="default" placeholder="输入会话ID">
                </div>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="getSessionStatus()">🔍 查询状态</button>
                </div>
                <div class="status-panel" id="sessionStatus" style="display: none;">
                    <div id="sessionStatusContent"></div>
                </div>
            </div>
            
            <!-- 关键词管理 -->
            <div class="section">
                <h3>🏷️ 打断关键词</h3>
                <div id="keywordList">
                    <!-- 关键词标签将在这里显示 -->
                </div>
                <div class="control-group" style="margin-top: 15px;">
                    <label for="newKeyword">添加新关键词:</label>
                    <input type="text" id="newKeyword" placeholder="输入新的打断关键词">
                </div>
                <div class="button-group">
                    <button class="btn btn-success" onclick="addKeyword()">➕ 添加</button>
                    <button class="btn btn-warning" onclick="resetKeywords()">🔄 重置默认</button>
                </div>
            </div>
            
            <!-- 日志面板 -->
            <div class="section">
                <h3>📝 操作日志</h3>
                <div class="log-panel" id="logPanel"></div>
                <div class="button-group" style="margin-top: 10px;">
                    <button class="btn btn-warning" onclick="clearLog()">🗑️ 清空日志</button>
                </div>
            </div>
        </div>
    </div>

    <script src="interrupt_control.js"></script>
</body>
</html>
