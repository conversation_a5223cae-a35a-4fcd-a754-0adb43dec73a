<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多会话数字人演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .session-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .session-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .session-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .session-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .config-form {
            margin-bottom: 15px;
        }
        .form-group {
            margin-bottom: 10px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .message-area {
            margin-top: 15px;
        }
        .message-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        .video-container {
            width: 100%;
            height: 200px;
            background-color: #000;
            border-radius: 4px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .control-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .stats-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .log-area {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>多会话数字人演示</h1>
            <p>支持不同形象和声音的多个数字人会话</p>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <h3>控制面板</h3>
            <div class="button-group">
                <button class="btn btn-success" onclick="createDefaultSession()">创建默认会话</button>
                <button class="btn btn-primary" onclick="createCustomSession()">创建自定义会话</button>
                <button class="btn btn-warning" onclick="refreshSessions()">刷新会话列表</button>
                <button class="btn btn-danger" onclick="clearAllSessions()">清空所有会话</button>
            </div>
            
            <!-- 快速创建模板 -->
            <div class="form-group">
                <label>快速创建模板:</label>
                <select id="templateSelect">
                    <option value="">选择模板...</option>
                    <option value="female_voice_1">女声1 - 晓晓</option>
                    <option value="male_voice_1">男声1 - 云希</option>
                    <option value="wav2lip_template">Wav2Lip模型</option>
                    <option value="gpt_sovits_template">GPT-SoVITS</option>
                </select>
                <button class="btn btn-primary" onclick="createFromTemplate()" style="margin-top: 10px;">从模板创建</button>
            </div>
        </div>

        <!-- 统计面板 -->
        <div class="stats-panel">
            <h3>系统统计</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="activeSessionsCount">0</div>
                    <div class="stat-label">活跃会话</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="cachedModelsCount">0</div>
                    <div class="stat-label">缓存模型</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="cachedAvatarsCount">0</div>
                    <div class="stat-label">缓存形象</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalMemoryUsage">0MB</div>
                    <div class="stat-label">内存使用</div>
                </div>
            </div>
        </div>

        <!-- 会话网格 -->
        <div id="sessionGrid" class="session-grid">
            <!-- 会话卡片将动态生成 -->
        </div>

        <!-- 日志区域 -->
        <div class="control-panel">
            <h3>操作日志</h3>
            <div id="logArea" class="log-area"></div>
            <button class="btn btn-warning" onclick="clearLog()" style="margin-top: 10px;">清空日志</button>
        </div>
    </div>

    <script>
        // 全局变量
        let sessions = {};
        let logMessages = [];

        // 日志函数
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            logMessages.push(logMessage);
            
            const logArea = document.getElementById('logArea');
            logArea.innerHTML = logMessages.slice(-50).join('\n'); // 只保留最近50条
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            logMessages = [];
            document.getElementById('logArea').innerHTML = '';
        }

        // API调用函数
        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(endpoint, options);
                const result = await response.json();
                
                if (result.code !== 0) {
                    throw new Error(result.msg || 'API调用失败');
                }
                
                return result;
            } catch (error) {
                log(`API调用错误: ${error.message}`);
                throw error;
            }
        }

        // 创建默认会话
        async function createDefaultSession() {
            try {
                log('正在创建默认会话...');
                
                const sessionConfig = {
                    avatar_id: 'avator_1',
                    model: 'musetalk',
                    tts: 'edgetts',
                    voice: 'zh-CN-YunxiaNeural'
                };
                
                // 这里需要真实的WebRTC SDP，简化演示
                const offerData = {
                    sdp: 'v=0\no=- 0 0 IN IP4 127.0.0.1\ns=-\nc=IN IP4 127.0.0.1\nt=0 0\nm=video 9 UDP/TLS/RTP/SAVPF 96\na=rtpmap:96 H264/90000\na=sendrecv\nm=audio 9 UDP/TLS/RTP/SAVPF 111\na=rtpmap:111 opus/48000/2\na=sendrecv',
                    type: 'offer',
                    session_config: sessionConfig
                };
                
                const result = await apiCall('/offer_with_config', 'POST', offerData);
                log(`成功创建会话 ${result.session_id}`);
                
                await refreshSessions();
            } catch (error) {
                log(`创建默认会话失败: ${error.message}`);
            }
        }

        // 从模板创建会话
        async function createFromTemplate() {
            const templateSelect = document.getElementById('templateSelect');
            const templateName = templateSelect.value;
            
            if (!templateName) {
                alert('请选择一个模板');
                return;
            }
            
            const templates = {
                female_voice_1: {
                    avatar_id: 'female_avatar_1',
                    model: 'musetalk',
                    tts: 'edgetts',
                    voice: 'zh-CN-XiaoxiaoNeural'
                },
                male_voice_1: {
                    avatar_id: 'male_avatar_1',
                    model: 'musetalk',
                    tts: 'edgetts',
                    voice: 'zh-CN-YunxiNeural'
                },
                wav2lip_template: {
                    avatar_id: 'wav2lip_avatar_1',
                    model: 'wav2lip',
                    tts: 'edgetts',
                    voice: 'zh-CN-YunjianNeural'
                },
                gpt_sovits_template: {
                    avatar_id: 'avator_1',
                    model: 'musetalk',
                    tts: 'gpt-sovits',
                    voice: 'reference_audio.wav',
                    ref_text: '这是参考文本'
                }
            };
            
            const sessionConfig = templates[templateName];
            if (!sessionConfig) {
                alert('未知的模板');
                return;
            }
            
            try {
                log(`正在从模板 ${templateName} 创建会话...`);
                
                const offerData = {
                    sdp: 'v=0\no=- 0 0 IN IP4 127.0.0.1\ns=-\nc=IN IP4 127.0.0.1\nt=0 0\nm=video 9 UDP/TLS/RTP/SAVPF 96\na=rtpmap:96 H264/90000\na=sendrecv\nm=audio 9 UDP/TLS/RTP/SAVPF 111\na=rtpmap:111 opus/48000/2\na=sendrecv',
                    type: 'offer',
                    session_config: sessionConfig
                };
                
                const result = await apiCall('/offer_with_config', 'POST', offerData);
                log(`成功从模板创建会话 ${result.session_id}`);
                
                await refreshSessions();
            } catch (error) {
                log(`从模板创建会话失败: ${error.message}`);
            }
        }

        // 刷新会话列表
        async function refreshSessions() {
            try {
                const result = await apiCall('/sessions');
                sessions = result.sessions || {};
                
                renderSessions();
                updateStats();
                
                log(`刷新会话列表，共 ${Object.keys(sessions).length} 个会话`);
            } catch (error) {
                log(`刷新会话列表失败: ${error.message}`);
            }
        }

        // 渲染会话卡片
        function renderSessions() {
            const sessionGrid = document.getElementById('sessionGrid');
            sessionGrid.innerHTML = '';
            
            for (const [sessionId, sessionData] of Object.entries(sessions)) {
                const sessionCard = createSessionCard(sessionId, sessionData);
                sessionGrid.appendChild(sessionCard);
            }
        }

        // 创建会话卡片
        function createSessionCard(sessionId, sessionData) {
            const card = document.createElement('div');
            card.className = 'session-card';
            card.innerHTML = `
                <div class="session-header">
                    <div class="session-title">会话 ${sessionId}</div>
                    <div class="session-status status-active">活跃</div>
                </div>
                
                <div class="video-container">
                    视频流区域
                </div>
                
                <div class="config-form">
                    <div class="form-group">
                        <label>形象ID:</label>
                        <input type="text" value="${sessionData.avatar_id}" onchange="updateSessionConfig(${sessionId}, 'avatar_id', this.value)">
                    </div>
                    <div class="form-group">
                        <label>模型:</label>
                        <select onchange="updateSessionConfig(${sessionId}, 'model', this.value)">
                            <option value="musetalk" ${sessionData.model === 'musetalk' ? 'selected' : ''}>MuseTalk</option>
                            <option value="wav2lip" ${sessionData.model === 'wav2lip' ? 'selected' : ''}>Wav2Lip</option>
                            <option value="ultralight" ${sessionData.model === 'ultralight' ? 'selected' : ''}>UltraLight</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>TTS类型:</label>
                        <select onchange="updateSessionConfig(${sessionId}, 'tts', this.value)">
                            <option value="edgetts" ${sessionData.tts === 'edgetts' ? 'selected' : ''}>EdgeTTS</option>
                            <option value="gpt-sovits" ${sessionData.tts === 'gpt-sovits' ? 'selected' : ''}>GPT-SoVITS</option>
                            <option value="cosyvoice" ${sessionData.tts === 'cosyvoice' ? 'selected' : ''}>CosyVoice</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>声音:</label>
                        <input type="text" value="${sessionData.voice}" onchange="updateSessionConfig(${sessionId}, 'voice', this.value)">
                    </div>
                </div>
                
                <div class="button-group">
                    <button class="btn btn-primary" onclick="testSession(${sessionId})">测试</button>
                    <button class="btn btn-danger" onclick="deleteSession(${sessionId})">删除</button>
                </div>
                
                <div class="message-area">
                    <input type="text" class="message-input" placeholder="输入消息..." id="message-${sessionId}">
                    <button class="btn btn-success" onclick="sendMessage(${sessionId})">发送消息</button>
                </div>
            `;
            
            return card;
        }

        // 更新会话配置
        async function updateSessionConfig(sessionId, key, value) {
            try {
                const config = {};
                config[key] = value;
                
                await apiCall('/update_session_config', 'POST', {
                    session_id: sessionId,
                    config: config
                });
                
                log(`更新会话 ${sessionId} 的 ${key} 为 ${value}`);
                await refreshSessions();
            } catch (error) {
                log(`更新会话配置失败: ${error.message}`);
            }
        }

        // 发送消息
        async function sendMessage(sessionId) {
            const messageInput = document.getElementById(`message-${sessionId}`);
            const message = messageInput.value.trim();
            
            if (!message) {
                alert('请输入消息');
                return;
            }
            
            try {
                await apiCall('/human', 'POST', {
                    sessionid: sessionId,
                    text: message,
                    type: 'chat'
                });
                
                log(`向会话 ${sessionId} 发送消息: ${message}`);
                messageInput.value = '';
            } catch (error) {
                log(`发送消息失败: ${error.message}`);
            }
        }

        // 测试会话
        async function testSession(sessionId) {
            await sendMessage(sessionId);
        }

        // 删除会话
        async function deleteSession(sessionId) {
            if (!confirm(`确定要删除会话 ${sessionId} 吗？`)) {
                return;
            }
            
            try {
                await apiCall(`/session/${sessionId}`, 'DELETE');
                log(`删除会话 ${sessionId}`);
                await refreshSessions();
            } catch (error) {
                log(`删除会话失败: ${error.message}`);
            }
        }

        // 清空所有会话
        async function clearAllSessions() {
            if (!confirm('确定要清空所有会话吗？')) {
                return;
            }
            
            for (const sessionId of Object.keys(sessions)) {
                try {
                    await apiCall(`/session/${sessionId}`, 'DELETE');
                } catch (error) {
                    log(`删除会话 ${sessionId} 失败: ${error.message}`);
                }
            }
            
            log('清空所有会话');
            await refreshSessions();
        }

        // 更新统计信息
        async function updateStats() {
            try {
                const result = await apiCall('/resource_stats');
                const stats = result.stats || {};
                
                document.getElementById('activeSessionsCount').textContent = Object.keys(sessions).length;
                document.getElementById('cachedModelsCount').textContent = stats.models?.cached || 0;
                document.getElementById('cachedAvatarsCount').textContent = stats.avatars?.cached || 0;
                document.getElementById('totalMemoryUsage').textContent = '计算中...';
            } catch (error) {
                log(`获取统计信息失败: ${error.message}`);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('多会话演示页面已加载');
            refreshSessions();
            
            // 定期刷新统计信息
            setInterval(updateStats, 10000);
        });
    </script>
</body>
</html>
