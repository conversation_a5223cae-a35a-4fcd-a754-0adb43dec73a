"""
增强版应用 V2 - 支持Nacos配置自动刷新
"""
import os
import sys
import logging
import asyncio
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入原始app模块的所有内容
from app import *
from multi_session_enhancement.api_extensions import APIExtensions
from multi_session_enhancement.session_manager import session_manager
from multi_session_enhancement.resource_manager import resource_manager
from multi_session_enhancement.nacos_config_manager_v2 import (
    init_config_manager_v2, get_config_manager_v2, ConfigChangeListener
)

logger = logging.getLogger(__name__)

class LiveTalkingConfigListener:
    """LiveTalking配置变更监听器"""
    
    def __init__(self):
        self.config_change_handlers = {
            'livetalking-api-config': self._handle_api_config_change,
            'livetalking-llm-config': self._handle_llm_config_change,
            'livetalking-tts-config': self._handle_tts_config_change
        }
    
    def _handle_api_config_change(self, data_id: str, new_config: Dict[str, Any]):
        """处理API配置变更"""
        logger.info(f"🔄 API配置已更新，重新加载相关服务...")
        
        # 这里可以添加重新初始化API客户端的逻辑
        # 例如：重新创建TTS客户端、LLM客户端等
        
        # 通知所有活跃的session更新配置
        self._notify_sessions_config_change('api', new_config)
    
    def _handle_llm_config_change(self, data_id: str, new_config: Dict[str, Any]):
        """处理LLM配置变更"""
        logger.info(f"🔄 LLM配置已更新: Model={new_config.get('model')}, Temperature={new_config.get('temperature')}")
        
        # 重新初始化LLM客户端
        self._reinitialize_llm_clients(new_config)
        
        # 通知sessions
        self._notify_sessions_config_change('llm', new_config)
    
    def _handle_tts_config_change(self, data_id: str, new_config: Dict[str, Any]):
        """处理TTS配置变更"""
        logger.info(f"🔄 TTS配置已更新，影响的提供商: {list(new_config.keys())}")
        
        # 重新初始化TTS客户端
        self._reinitialize_tts_clients(new_config)
        
        # 通知sessions
        self._notify_sessions_config_change('tts', new_config)
    
    def _reinitialize_llm_clients(self, new_config: Dict[str, Any]):
        """重新初始化LLM客户端"""
        try:
            # 这里可以添加重新创建LLM客户端的逻辑
            # 例如：更新全局的LLM配置，重新创建OpenAI客户端等
            logger.info("LLM客户端重新初始化完成")
        except Exception as e:
            logger.error(f"重新初始化LLM客户端失败: {e}")
    
    def _reinitialize_tts_clients(self, new_config: Dict[str, Any]):
        """重新初始化TTS客户端"""
        try:
            # 这里可以添加重新创建TTS客户端的逻辑
            # 例如：清除TTS客户端缓存，重新创建TTS实例等
            
            # 清除资源管理器中的TTS相关缓存
            if hasattr(resource_manager, 'clear_tts_cache'):
                resource_manager.clear_tts_cache()
            
            logger.info("TTS客户端重新初始化完成")
        except Exception as e:
            logger.error(f"重新初始化TTS客户端失败: {e}")
    
    def _notify_sessions_config_change(self, config_type: str, new_config: Dict[str, Any]):
        """通知所有活跃的session配置已变更"""
        try:
            # 获取所有活跃的session
            active_sessions = getattr(session_manager, 'get_active_sessions', lambda: [])()
            
            for session_id in active_sessions:
                logger.info(f"通知session {session_id} 配置变更: {config_type}")
                # 这里可以添加具体的通知逻辑
                # 例如：发送WebSocket消息通知前端配置已更新
        except Exception as e:
            logger.error(f"通知session配置变更失败: {e}")
    
    def create_listener(self, data_id: str) -> ConfigChangeListener:
        """创建指定配置的监听器"""
        handler = self.config_change_handlers.get(data_id)
        if handler:
            return ConfigChangeListener(lambda data_id, config: handler(data_id, config))
        else:
            return ConfigChangeListener(lambda data_id, config: logger.info(f"未知配置变更: {data_id}"))

def setup_enhanced_app_v2():
    """设置增强版应用V2"""
    global appasync
    
    logger.info("🚀 启动增强版LiveTalking应用V2（支持配置自动刷新）")
    
    # 1. 初始化Nacos配置管理器
    nacos_client = get_nacos_client()
    if nacos_client:
        config_manager = init_config_manager_v2(
            nacos_client,
            enable_auto_refresh=True,
            refresh_interval=30  # 30秒检查一次配置变更
        )
        logger.info("✅ Nacos配置管理器V2初始化成功")
        
        # 2. 设置配置变更监听器
        config_listener = LiveTalkingConfigListener()
        
        # 添加各种配置的监听器
        api_listener = config_listener.create_listener("livetalking-api-config")
        llm_listener = config_listener.create_listener("livetalking-llm-config")
        tts_listener = config_listener.create_listener("livetalking-tts-config")
        
        config_manager.add_config_listener("livetalking-api-config", api_listener)
        config_manager.add_config_listener("livetalking-llm-config", llm_listener)
        config_manager.add_config_listener("livetalking-tts-config", tts_listener)
        
        logger.info("✅ 配置变更监听器设置完成")
    else:
        logger.warning("⚠️  Nacos客户端未初始化，配置管理器将降级到环境变量")
        config_manager = init_config_manager_v2()
    
    # 3. 替换原始的offer路由
    # 移除原始路由
    for route in list(appasync.router.routes()):
        if hasattr(route, '_path') and route._path == '/offer':
            appasync.router._resources.remove(route._resource)
    
    # 添加增强版路由
    appasync.router.add_post("/offer", enhanced_offer)
    
    # 4. 添加API扩展
    api_extensions = APIExtensions(appasync, enhanced_build_nerfreal, nerfreals)
    
    # 5. 添加配置管理相关的API端点
    setup_config_management_apis(appasync, config_manager)
    
    # 6. 设置默认配置
    default_config = {
        'avatar_id': opt.avatar_id,
        'model': opt.model,
        'tts': opt.tts,
        'voice': opt.REF_FILE,
        'ref_text': opt.REF_TEXT,
        'tts_server': opt.TTS_SERVER,
        'batch_size': opt.batch_size,
        'fps': opt.fps,
        'W': opt.W,
        'H': opt.H,
        'customvideo_config': opt.customvideo_config
    }
    session_manager.set_default_config(default_config)
    
    logger.info("✅ 增强版多会话支持已启用")
    logger.info("✅ Nacos配置自动刷新已启用")

def setup_config_management_apis(app, config_manager):
    """设置配置管理相关的API端点"""
    
    @app.router.add_get("/api/config/status")
    async def get_config_status(request):
        """获取配置状态"""
        try:
            if config_manager:
                cache_info = config_manager.get_cache_info()
                status = {
                    "nacos_connected": config_manager.client is not None,
                    "auto_refresh_enabled": config_manager.enable_auto_refresh,
                    "refresh_interval": config_manager.refresh_interval,
                    "cache_info": cache_info
                }
            else:
                status = {
                    "nacos_connected": False,
                    "auto_refresh_enabled": False,
                    "fallback_mode": "environment_variables"
                }
            
            return web.json_response(status)
        except Exception as e:
            logger.error(f"获取配置状态失败: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    @app.router.add_post("/api/config/refresh")
    async def force_refresh_config(request):
        """强制刷新配置"""
        try:
            if config_manager:
                config_manager.force_refresh()
                return web.json_response({"message": "配置刷新成功"})
            else:
                return web.json_response({"error": "配置管理器未初始化"}, status=400)
        except Exception as e:
            logger.error(f"强制刷新配置失败: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    @app.router.add_get("/api/config/current")
    async def get_current_config(request):
        """获取当前配置"""
        try:
            from multi_session_enhancement.nacos_config_manager_v2 import (
                get_api_config_v2, get_llm_config_v2, get_tts_config_v2
            )
            
            # 获取当前配置（隐藏敏感信息）
            api_config = get_api_config_v2()
            llm_config = get_llm_config_v2()
            
            # 隐藏敏感信息
            safe_api_config = {
                "dashscope_api_key": "***" if api_config.dashscope_api_key else "",
                "tencent_appid": api_config.tencent_appid,
                "doubao_appid": api_config.doubao_appid,
                "openai_base_url": api_config.openai_base_url
            }
            
            safe_llm_config = {
                "provider": llm_config.provider,
                "base_url": llm_config.base_url,
                "model": llm_config.model,
                "max_tokens": llm_config.max_tokens,
                "temperature": llm_config.temperature
            }
            
            # TTS配置
            tts_configs = {}
            for provider in ["edgetts", "tencent", "doubao"]:
                tts_config = get_tts_config_v2(provider)
                tts_configs[provider] = {
                    "provider": tts_config.provider,
                    "voice": tts_config.voice,
                    "speed": tts_config.speed
                }
            
            return web.json_response({
                "api_config": safe_api_config,
                "llm_config": safe_llm_config,
                "tts_configs": tts_configs
            })
        except Exception as e:
            logger.error(f"获取当前配置失败: {e}")
            return web.json_response({"error": str(e)}, status=500)

def enhanced_build_nerfreal(opt, sessionid: int):
    """增强版nerfreal构建函数 - 支持配置自动刷新"""
    try:
        # 使用最新的配置构建nerfreal实例
        from multi_session_enhancement.nacos_config_manager_v2 import get_tts_config_v2
        
        # 获取当前TTS配置
        tts_config = get_tts_config_v2(opt.tts)
        
        # 更新opt中的TTS相关配置
        if hasattr(opt, 'REF_FILE'):
            opt.REF_FILE = tts_config.voice
        
        # 调用原始的构建函数
        return build_nerfreal(opt, sessionid)
        
    except Exception as e:
        logger.error(f"构建nerfreal失败: {e}")
        # 降级到原始构建方式
        return build_nerfreal(opt, sessionid)

async def enhanced_offer(request):
    """增强版offer处理函数"""
    try:
        # 使用最新配置处理请求
        return await offer(request)
    except Exception as e:
        logger.error(f"处理offer请求失败: {e}")
        return web.json_response({"error": str(e)}, status=500)

def main():
    """主函数"""
    # 设置增强版应用
    setup_enhanced_app_v2()
    
    # 启动应用
    logger.info(f"🌟 启动LiveTalking增强版服务，端口: {opt.port}")
    web.run_app(appasync, host="0.0.0.0", port=opt.port)

if __name__ == "__main__":
    main()
