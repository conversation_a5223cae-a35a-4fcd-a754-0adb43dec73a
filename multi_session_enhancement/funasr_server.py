"""
FunASR服务器 - 支持WebSocket和OpenAI API两种接入方式
基于FunASR实现的语音识别服务
"""
import asyncio
import json
import logging
import time
import wave
import io
import base64
import tempfile
import os
from typing import Dict, List, Optional, Any
import numpy as np
import aiohttp
from aiohttp import web, WSMsgType
import websockets
from dataclasses import dataclass, asdict

# FunASR相关导入
try:
    from funasr import AutoModel
    from funasr.utils.postprocess_utils import rich_transcription_postprocess
    FUNASR_AVAILABLE = True
except ImportError:
    FUNASR_AVAILABLE = False
    logging.warning("FunASR未安装，将使用模拟模式")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FunASRConfig:
    """FunASR配置"""
    model_path: str = "iic/SenseVoiceSmall"  # 或者 "paraformer-zh" 等
    device: str = "cuda"                     # cuda 或 cpu
    batch_size: int = 1
    host: str = "0.0.0.0"
    port: int = 10096
    
    # WebSocket配置
    chunk_size: List[int] = None            # [5, 10, 5] FunASR chunk配置
    chunk_interval: int = 10                # 块间隔
    
    # 识别配置
    language: str = "auto"                  # 语言设置
    use_itn: bool = True                   # 逆文本标准化
    use_timestamp: bool = True             # 时间戳
    
    def __post_init__(self):
        if self.chunk_size is None:
            self.chunk_size = [5, 10, 5]

class AudioBuffer:
    """音频缓冲区管理"""
    
    def __init__(self, sample_rate: int = 16000, max_duration: int = 30):
        self.sample_rate = sample_rate
        self.max_samples = sample_rate * max_duration
        self.buffer = np.array([], dtype=np.int16)
        self.last_activity = time.time()
    
    def add_audio(self, audio_data: bytes):
        """添加音频数据"""
        if isinstance(audio_data, (bytes, bytearray)):
            # 处理PCM数据
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
        else:
            # 处理numpy数组
            audio_array = np.array(audio_data, dtype=np.int16)
        
        self.buffer = np.concatenate([self.buffer, audio_array])
        
        # 限制缓冲区大小
        if len(self.buffer) > self.max_samples:
            self.buffer = self.buffer[-self.max_samples:]
        
        self.last_activity = time.time()
    
    def get_audio_wav(self) -> bytes:
        """获取WAV格式音频"""
        if len(self.buffer) == 0:
            return b''
        
        # 创建WAV文件
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(self.sample_rate)
            wav_file.writeframes(self.buffer.tobytes())
        
        return wav_buffer.getvalue()
    
    def clear(self):
        """清空缓冲区"""
        self.buffer = np.array([], dtype=np.int16)
    
    def duration(self) -> float:
        """获取音频时长(秒)"""
        return len(self.buffer) / self.sample_rate
    
    def is_silent(self, threshold: float = 0.01) -> bool:
        """检测是否静音"""
        if len(self.buffer) == 0:
            return True
        
        # 计算音频能量
        energy = np.mean(np.abs(self.buffer.astype(np.float32))) / 32768.0
        return energy < threshold

class FunASRModel:
    """FunASR模型包装器"""
    
    def __init__(self, config: FunASRConfig):
        self.config = config
        self.model = None
        self.vad_model = None
        self.punc_model = None
        
    async def load_model(self):
        """异步加载模型"""
        try:
            if FUNASR_AVAILABLE:
                logger.info(f"加载FunASR模型: {self.config.model_path}")
                
                # 加载主模型
                self.model = AutoModel(
                    model=self.config.model_path,
                    trust_remote_code=True,
                    device=self.config.device,
                    disable_update=True
                )
                
                # 可选：加载VAD模型
                try:
                    self.vad_model = AutoModel(
                        model="fsmn-vad",
                        device=self.config.device,
                        disable_update=True
                    )
                except:
                    logger.warning("VAD模型加载失败，将跳过VAD处理")
                
                # 可选：加载标点模型
                try:
                    self.punc_model = AutoModel(
                        model="ct-punc",
                        device=self.config.device,
                        disable_update=True
                    )
                except:
                    logger.warning("标点模型加载失败，将跳过标点处理")
                
                logger.info("FunASR模型加载完成")
            else:
                logger.warning("使用模拟模式，请安装FunASR")
                self.model = "mock_model"
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    async def transcribe(self, audio_data: bytes, language: str = None, use_itn: bool = True) -> Dict[str, Any]:
        """转录音频"""
        try:
            if not FUNASR_AVAILABLE:
                # 模拟转录结果
                return {
                    "text": "这是模拟的转录结果，请安装FunASR以使用真实模型",
                    "timestamp": [],
                    "language": language or "zh"
                }
            
            # 保存临时音频文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name
            
            try:
                # 使用FunASR进行转录
                result = self.model.generate(
                    input=temp_path,
                    cache={},
                    language=language or self.config.language,
                    use_itn=use_itn,
                    batch_size_s=60,
                    merge_vad=True,
                    merge_length_s=15
                )
                
                # 处理结果
                if isinstance(result, list) and len(result) > 0:
                    res = result[0]
                    text = res.get('text', '')
                    timestamp = res.get('timestamp', [])
                    
                    return {
                        "text": text.strip(),
                        "timestamp": timestamp,
                        "language": language or self.config.language,
                        "duration": len(np.frombuffer(audio_data[44:], dtype=np.int16)) / 16000.0 if len(audio_data) > 44 else 0.0
                    }
                else:
                    return {
                        "text": "",
                        "timestamp": [],
                        "language": language or self.config.language,
                        "duration": 0.0
                    }
                    
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except Exception as e:
            logger.error(f"转录失败: {e}")
            return {
                "text": "",
                "timestamp": [],
                "language": language or self.config.language,
                "duration": 0.0,
                "error": str(e)
            }

class FunASRWebSocketHandler:
    """FunASR WebSocket处理器 - 兼容原始FunASR协议"""
    
    def __init__(self, model: FunASRModel):
        self.model = model
        self.active_sessions: Dict[str, Dict] = {}
    
    async def handle_websocket(self, request):
        """处理WebSocket连接"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        session_id = request.query.get('session_id', f"session_{int(time.time())}")
        logger.info(f"新的WebSocket连接: {session_id}")
        
        # 初始化会话
        audio_buffer = AudioBuffer(16000, 30)
        session_info = {
            'ws': ws,
            'buffer': audio_buffer,
            'config': None,
            'last_transcription': time.time(),
            'is_speaking': False,
            'mode': '2pass'  # 默认模式
        }
        self.active_sessions[session_id] = session_info
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    # 处理JSON配置消息
                    try:
                        data = json.loads(msg.data)
                        await self._handle_config_message(session_id, data)
                    except json.JSONDecodeError:
                        logger.error(f"无效的JSON消息: {msg.data}")
                
                elif msg.type == WSMsgType.BINARY:
                    # 处理音频数据
                    await self._handle_audio_message(session_id, msg.data)
                
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket错误: {ws.exception()}')
                    break
        
        except Exception as e:
            logger.error(f"WebSocket处理错误: {e}")
        
        finally:
            # 清理会话
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            logger.info(f"WebSocket连接关闭: {session_id}")
        
        return ws
    
    async def _handle_config_message(self, session_id: str, data: Dict[str, Any]):
        """处理配置消息"""
        session = self.active_sessions[session_id]
        session['config'] = data
        
        # 提取配置参数
        session['mode'] = data.get('mode', '2pass')
        session['use_itn'] = data.get('itn', True)
        session['language'] = data.get('language', 'auto')
        
        logger.info(f"会话 {session_id} 配置: {data}")
        
        # 发送确认消息 (兼容FunASR格式)
        response = {
            "status": "ok",
            "message": "connected"
        }
        await session['ws'].send_text(json.dumps(response))
    
    async def _handle_audio_message(self, session_id: str, audio_data: bytes):
        """处理音频消息"""
        session = self.active_sessions[session_id]
        buffer = session['buffer']
        
        # 添加音频到缓冲区
        buffer.add_audio(audio_data)
        session['is_speaking'] = not buffer.is_silent()
        
        # 检查是否需要进行转录
        current_time = time.time()
        time_since_last = current_time - session['last_transcription']
        mode = session.get('mode', '2pass')
        
        # 根据模式决定转录策略
        should_transcribe = False
        
        if mode == 'online':
            # 在线模式：频繁转录
            should_transcribe = (buffer.duration() >= 1.0 and time_since_last >= 0.5)
        elif mode == 'offline':
            # 离线模式：等待静音或长时间
            should_transcribe = (buffer.duration() >= 2.0 and 
                               (buffer.is_silent() or buffer.duration() >= 10.0))
        else:  # 2pass模式
            # 2pass模式：平衡实时性和准确性
            should_transcribe = (buffer.duration() >= 1.5 and 
                               time_since_last >= 1.0 and
                               (buffer.is_silent() or buffer.duration() >= 8.0))
        
        if should_transcribe:
            await self._perform_transcription(session_id)
    
    async def _perform_transcription(self, session_id: str):
        """执行转录"""
        session = self.active_sessions[session_id]
        buffer = session['buffer']
        config = session.get('config', {})
        
        if buffer.duration() < 0.5:  # 音频太短，跳过
            return
        
        try:
            # 获取音频数据
            wav_data = buffer.get_audio_wav()
            
            # 执行转录
            result = await self.model.transcribe(
                wav_data, 
                config.get('language', 'auto'),
                config.get('itn', True)
            )
            
            text = result.get('text', '').strip()
            
            if text:
                # 构建FunASR兼容的响应格式
                mode = session.get('mode', '2pass')
                is_final = mode in ['offline', '2pass-offline']
                
                response = {
                    "text": text,
                    "mode": mode,
                    "is_final": is_final,
                    "timestamp": result.get('timestamp', []),
                    "language": result.get('language', 'auto'),
                    "session_id": session_id
                }
                
                await session['ws'].send_text(json.dumps(response))
                logger.info(f"转录结果 [{session_id}]: {text}")
            
            # 清空缓冲区
            buffer.clear()
            session['last_transcription'] = time.time()
            
        except Exception as e:
            logger.error(f"转录执行失败: {e}")

class FunASROpenAIHandler:
    """FunASR OpenAI API处理器"""
    
    def __init__(self, model: FunASRModel):
        self.model = model
    
    async def transcribe_audio(self, request):
        """音频转录API - OpenAI兼容格式"""
        try:
            # 解析请求
            if request.content_type == 'application/json':
                # JSON格式请求
                data = await request.json()
                audio_b64 = data.get('audio', '')
                language = data.get('language', 'auto')
                use_itn = data.get('use_itn', True)
                
                if not audio_b64:
                    return web.json_response({"error": "缺少音频数据"}, status=400)
                
                # 解码base64音频
                try:
                    audio_data = base64.b64decode(audio_b64)
                except Exception as e:
                    return web.json_response({"error": f"音频解码失败: {e}"}, status=400)
            
            else:
                # multipart/form-data格式请求
                reader = await request.multipart()
                audio_data = None
                language = 'auto'
                use_itn = True
                
                async for field in reader:
                    if field.name in ['file', 'audio']:
                        audio_data = await field.read()
                    elif field.name == 'language':
                        language = await field.text()
                    elif field.name == 'use_itn':
                        use_itn = (await field.text()).lower() == 'true'
                
                if not audio_data:
                    return web.json_response({"error": "未找到音频文件"}, status=400)
            
            # 执行转录
            result = await self.model.transcribe(audio_data, language, use_itn)
            text = result.get('text', '')
            
            # 返回OpenAI兼容格式
            response = {
                "text": text,
                "task": "transcribe",
                "language": result.get('language', language),
                "duration": result.get('duration', 0.0),
                "segments": [
                    {
                        "id": 0,
                        "seek": 0,
                        "start": 0.0,
                        "end": result.get('duration', 0.0),
                        "text": text,
                        "tokens": [],
                        "temperature": 0.0,
                        "avg_logprob": 0.0,
                        "compression_ratio": 0.0,
                        "no_speech_prob": 0.0
                    }
                ] if text else []
            }
            
            return web.json_response(response)
            
        except Exception as e:
            logger.error(f"转录请求处理失败: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def list_models(self, request):
        """列出可用模型"""
        models = [
            {
                "id": "funasr-sensevoice",
                "object": "model",
                "created": 1677610602,
                "owned_by": "funasr",
                "permission": [],
                "root": "funasr-sensevoice",
                "parent": None
            }
        ]
        
        return web.json_response({
            "object": "list",
            "data": models
        })

def create_app(config: FunASRConfig) -> web.Application:
    """创建FunASR应用"""
    app = web.Application(client_max_size=100*1024*1024)  # 100MB max file size
    
    # 创建模型实例
    model = FunASRModel(config)
    
    # 创建处理器
    ws_handler = FunASRWebSocketHandler(model)
    api_handler = FunASROpenAIHandler(model)
    
    # WebSocket路由 (兼容FunASR)
    app.router.add_get('/ws', ws_handler.handle_websocket)
    app.router.add_get('/wss', ws_handler.handle_websocket)  # 兼容路由
    
    # OpenAI兼容API路由
    app.router.add_post('/v1/audio/transcriptions', api_handler.transcribe_audio)
    app.router.add_get('/v1/models', api_handler.list_models)
    
    # 兼容路由
    app.router.add_post('/audio/transcriptions', api_handler.transcribe_audio)
    app.router.add_get('/models', api_handler.list_models)
    
    # 健康检查
    async def health_check(request):
        return web.json_response({
            "status": "healthy",
            "model": config.model_path,
            "device": config.device,
            "funasr_available": FUNASR_AVAILABLE
        })
    
    app.router.add_get('/health', health_check)
    
    # 存储模型实例以便初始化
    app['model'] = model
    
    return app

async def init_app(app):
    """初始化应用"""
    model = app['model']
    await model.load_model()

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='FunASR服务器')
    parser.add_argument('--model', default='iic/SenseVoiceSmall', help='模型路径')
    parser.add_argument('--device', default='cuda', help='设备类型')
    parser.add_argument('--host', default='0.0.0.0', help='服务器地址')
    parser.add_argument('--port', type=int, default=10096, help='服务器端口')
    
    args = parser.parse_args()
    
    # 创建配置
    config = FunASRConfig(
        model_path=args.model,
        device=args.device,
        host=args.host,
        port=args.port
    )
    
    # 创建应用
    app = create_app(config)
    
    # 初始化
    await init_app(app)
    
    # 启动服务器
    runner = web.AppRunner(app)
    await runner.setup()
    
    site = web.TCPSite(runner, args.host, args.port)
    await site.start()
    
    logger.info(f"FunASR服务器启动在 http://{args.host}:{args.port}")
    logger.info(f"模型: {args.model}")
    logger.info(f"WebSocket地址: ws://{args.host}:{args.port}/ws")
    logger.info(f"OpenAI API地址: http://{args.host}:{args.port}/v1/audio/transcriptions")
    logger.info(f"健康检查: http://{args.host}:{args.port}/health")
    
    # 保持服务运行
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("服务器停止")
    finally:
        await runner.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
