{"api_config_sample": {"description": "API配置示例 - 数据ID: livetalking-api-config", "config": {"dashscope_api_key": "sk-your-dashscope-api-key-here", "tencent_appid": "**********", "tencent_secret_key": "your-tencent-secret-key", "tencent_secret_id": "your-tencent-secret-id", "doubao_appid": "your-doubao-appid", "doubao_token": "your-doubao-token", "openai_api_key": "sk-your-openai-api-key", "openai_base_url": "https://api.openai.com/v1", "azure_speech_key": "your-azure-speech-key", "azure_speech_region": "eastus"}}, "llm_config_sample": {"description": "LLM配置示例 - 数据ID: livetalking-llm-config", "config": {"provider": "dashscope", "api_key": "sk-your-dashscope-api-key-here", "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1", "model": "qwen-plus", "max_tokens": 8192, "temperature": 0.7}}, "tts_config_sample": {"description": "TTS配置示例 - 数据ID: livetalking-tts-config", "config": {"edgetts": {"provider": "edgetts", "voice": "zh-CN-YunxiaNeural", "speed": 1.0, "volume": 1.0, "pitch": 1.0}, "tencent": {"provider": "tencent", "app_id": "**********", "secret_key": "your-tencent-secret-key", "secret_id": "your-tencent-secret-id", "voice": "101001", "speed": 0, "volume": 0, "pitch": 0}, "doubao": {"provider": "do<PERSON>o", "app_id": "your-doubao-appid", "api_key": "your-doubao-token", "voice": "zh_female_shuang<PERSON><PERSON><PERSON>_moon_bigtts", "speed": 1.0, "volume": 1.0, "pitch": 1.0}, "gpt-sovits": {"provider": "gpt-sovits", "api_url": "http://localhost:9880", "voice": "reference_audio.wav", "ref_text": "这是参考文本，用于克隆语音风格", "speed": 1.0}, "cosyvoice": {"provider": "cosyvoice", "api_url": "http://localhost:50000", "voice": "中文女", "speed": 1.0}, "fishtts": {"provider": "fishtts", "api_url": "http://localhost:8080", "voice": "default", "speed": 1.0}}}, "voice_options": {"description": "各TTS提供商的语音选项", "edgetts_voices": ["zh-CN-YunxiaNeural", "zh-CN-YunxiNeural", "zh-CN-YunjianNeural", "zh-CN-XiaoxiaoNeural", "zh-CN-XiaoyiNeural", "zh-CN-<PERSON><PERSON>N<PERSON>al", "zh-C<PERSON>-<PERSON><PERSON>Neural", "zh-CN-XiaomengNeural", "zh-CN-XiaomoNeural", "zh-CN-XiaoqiuNeural", "zh-CN-XiaoruiNeural", "zh-CN-XiaoshuangNeural", "zh-CN-XiaoxuanNeural", "zh-CN-<PERSON>yanNeural", "zh-CN-XiaoyouNeural", "zh-CN-XiaozhenNeural"], "tencent_voices": ["101001", "101002", "101003", "101004", "101005", "101006", "101007", "101008", "101009", "101010", "101011", "101012"], "doubao_voices": ["zh_female_shuang<PERSON><PERSON><PERSON>_moon_bigtts", "zh_male_wennuan_moon_bigtts", "zh_female_tianmei_moon_bigtts", "zh_male_yifeng_moon_bigtts", "zh_female_shuangkuaishuang_moon_bigtts", "zh_male_chunhou_moon_bigtts"]}, "configuration_instructions": {"description": "配置说明", "steps": ["1. 登录Nacos控制台 (http://nacos-server:8848/nacos)", "2. 进入配置管理 -> 配置列表", "3. 点击 '+' 创建配置", "4. 填写以下信息:", "   - Data ID: livetalking-api-config (或其他配置ID)", "   - Group: DEFAULT_GROUP", "   - 配置格式: JSON", "   - 配置内容: 复制上面对应的config部分", "5. 点击发布", "6. 重复步骤3-5创建其他配置"], "data_ids": ["livetalking-api-config", "livetalking-llm-config", "livetalking-tts-config"], "group": "DEFAULT_GROUP", "format": "JSON"}, "environment_fallback": {"description": "环境变量降级映射", "api_config": {"dashscope_api_key": "DASHSCOPE_API_KEY", "tencent_appid": "TENCENT_APPID", "tencent_secret_key": "TENCENT_SECRET_KEY", "tencent_secret_id": "TENCENT_SECRET_ID", "doubao_appid": "DOUBAO_APPID", "doubao_token": "DOUBAO_TOKEN", "openai_api_key": "OPENAI_API_KEY", "openai_base_url": "OPENAI_BASE_URL"}, "llm_config": {"api_key": "DASHSCOPE_API_KEY", "base_url": "LLM_BASE_URL", "model": "LLM_MODEL"}}, "migration_guide": {"description": "从环境变量迁移到Nacos配置的指南", "before": {"description": "迁移前 - 使用环境变量", "code": "self.appid = os.getenv('TENCENT_APPID')"}, "after": {"description": "迁移后 - 使用Nacos配置", "code": ["from multi_session_enhancement.nacos_config_manager import get_tts_config", "tts_config = get_tts_config('tencent')", "self.appid = tts_config.app_id"]}, "benefits": ["集中化配置管理", "动态配置更新", "配置版本控制", "环境隔离", "配置审计", "降级保护"]}, "troubleshooting": {"description": "常见问题排查", "issues": [{"problem": "配置管理器初始化失败", "cause": "Nacos服务不可用或连接参数错误", "solution": "检查Nacos服务状态，验证连接参数"}, {"problem": "获取配置失败", "cause": "配置不存在或格式错误", "solution": "检查Nacos中的配置是否存在，验证JSON格式"}, {"problem": "API密钥无效", "cause": "配置中的API密钥错误或过期", "solution": "更新Nacos中的API密钥配置"}, {"problem": "降级到环境变量", "cause": "Nacos配置获取失败", "solution": "这是正常的降级行为，检查环境变量设置"}]}}