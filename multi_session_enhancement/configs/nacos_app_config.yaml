# Nacos应用配置文件
# 用于配置Nacos连接和配置管理相关参数

nacos:
  # Nacos服务器配置
  server_addresses: "127.0.0.1:8848"
  namespace: ""
  username: "nacos"
  password: "nacos"
  
  # 配置管理设置
  config:
    # 默认配置组
    default_group: "DEFAULT_GROUP"
    
    # 配置数据ID
    api_config_data_id: "livetalking-api-config"
    llm_config_data_id: "livetalking-llm-config"
    tts_config_data_id: "livetalking-tts-config"
    
    # 缓存设置
    cache_timeout: 300  # 5分钟
    
    # 配置格式 (json/yaml)
    config_format: "json"
    
    # 是否启用配置热更新
    enable_hot_reload: true
    
    # 热更新检查间隔（秒）
    hot_reload_interval: 30

# 配置模板定义
config_templates:
  # API配置模板
  api_config:
    # 阿里云DashScope
    dashscope_api_key: "your-dashscope-api-key"
    
    # 腾讯云
    tencent_appid: "your-tencent-appid"
    tencent_secret_key: "your-tencent-secret-key"
    tencent_secret_id: "your-tencent-secret-id"
    
    # 豆包/火山引擎
    doubao_appid: "your-doubao-appid"
    doubao_token: "your-doubao-token"
    
    # OpenAI
    openai_api_key: "your-openai-api-key"
    openai_base_url: "https://api.openai.com/v1"
    
    # Azure Speech
    azure_speech_key: "your-azure-speech-key"
    azure_speech_region: "your-azure-region"
  
  # LLM配置模板
  llm_config:
    provider: "dashscope"
    api_key: "your-api-key"
    base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
    model: "qwen-plus"
    max_tokens: 8192
    temperature: 0.7
  
  # TTS配置模板
  tts_config:
    # EdgeTTS配置
    edgetts:
      provider: "edgetts"
      voice: "zh-CN-YunxiaNeural"
      speed: 1.0
      volume: 1.0
      pitch: 1.0
    
    # 腾讯TTS配置
    tencent:
      provider: "tencent"
      app_id: "your-tencent-appid"
      secret_key: "your-tencent-secret-key"
      secret_id: "your-tencent-secret-id"
      voice: "101001"  # 腾讯云语音类型
      speed: 0
      volume: 0
      pitch: 0
    
    # 豆包TTS配置
    doubao:
      provider: "doubao"
      app_id: "your-doubao-appid"
      api_key: "your-doubao-token"
      voice: "zh_female_shuangkuaisisi_moon_bigtts"
      speed: 1.0
      volume: 1.0
      pitch: 1.0
    
    # GPT-SoVITS配置
    gpt-sovits:
      provider: "gpt-sovits"
      api_url: "http://localhost:9880"
      voice: "reference_audio.wav"
      ref_text: "参考文本"
      speed: 1.0
    
    # CosyVoice配置
    cosyvoice:
      provider: "cosyvoice"
      api_url: "http://localhost:50000"
      voice: "中文女"
      speed: 1.0
    
    # FishTTS配置
    fishtts:
      provider: "fishtts"
      api_url: "http://localhost:8080"
      voice: "default"
      speed: 1.0

# 环境变量映射
# 定义哪些配置项可以通过环境变量覆盖
env_override:
  api_config:
    dashscope_api_key: "DASHSCOPE_API_KEY"
    tencent_appid: "TENCENT_APPID"
    tencent_secret_key: "TENCENT_SECRET_KEY"
    tencent_secret_id: "TENCENT_SECRET_ID"
    doubao_appid: "DOUBAO_APPID"
    doubao_token: "DOUBAO_TOKEN"
    openai_api_key: "OPENAI_API_KEY"
    openai_base_url: "OPENAI_BASE_URL"
    azure_speech_key: "AZURE_SPEECH_KEY"
    azure_speech_region: "AZURE_SPEECH_REGION"
  
  llm_config:
    api_key: "DASHSCOPE_API_KEY"
    base_url: "LLM_BASE_URL"
    model: "LLM_MODEL"
  
  nacos:
    server_addresses: "NACOS_SERVER"
    namespace: "NACOS_NAMESPACE"
    username: "NACOS_USERNAME"
    password: "NACOS_PASSWORD"

# 配置验证规则
validation:
  api_config:
    required_fields:
      - "dashscope_api_key"
    optional_fields:
      - "tencent_appid"
      - "tencent_secret_key"
      - "tencent_secret_id"
      - "doubao_appid"
      - "doubao_token"
  
  llm_config:
    required_fields:
      - "api_key"
      - "base_url"
      - "model"
  
  tts_config:
    required_providers:
      - "edgetts"
    optional_providers:
      - "tencent"
      - "doubao"
      - "gpt-sovits"
      - "cosyvoice"
      - "fishtts"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 配置相关日志
  config_logger:
    name: "nacos_config"
    level: "DEBUG"
    
  # 是否记录配置变更
  log_config_changes: true
  
  # 是否记录敏感信息（API密钥等）
  log_sensitive_data: false
