{"templates": {"default": {"avatar_id": "avator_1", "model": "musetalk", "tts": "edgetts", "voice": "zh-CN-YunxiaNeural", "ref_text": null, "tts_server": "http://127.0.0.1:9880", "batch_size": 16, "fps": 50, "W": 450, "H": 450, "customvideo_config": ""}, "female_voice_1": {"avatar_id": "female_avatar_1", "model": "musetalk", "tts": "edgetts", "voice": "zh-CN-XiaoxiaoNeural", "ref_text": null, "tts_server": "http://127.0.0.1:9880", "batch_size": 16, "fps": 50, "W": 450, "H": 450, "customvideo_config": ""}, "male_voice_1": {"avatar_id": "male_avatar_1", "model": "musetalk", "tts": "edgetts", "voice": "zh-CN-YunxiNeural", "ref_text": null, "tts_server": "http://127.0.0.1:9880", "batch_size": 16, "fps": 50, "W": 450, "H": 450, "customvideo_config": ""}, "wav2lip_template": {"avatar_id": "wav2lip_avatar_1", "model": "wav2lip", "tts": "edgetts", "voice": "zh-CN-YunxiaNeural", "ref_text": null, "tts_server": "http://127.0.0.1:9880", "batch_size": 16, "fps": 50, "W": 450, "H": 450, "customvideo_config": ""}, "ultralight_template": {"avatar_id": "ultralight_avatar_1", "model": "ultralight", "tts": "edgetts", "voice": "zh-CN-YunxiaNeural", "ref_text": null, "tts_server": "http://127.0.0.1:9880", "batch_size": 16, "fps": 50, "W": 450, "H": 450, "customvideo_config": ""}, "gpt_sovits_template": {"avatar_id": "avator_1", "model": "musetalk", "tts": "gpt-sovits", "voice": "reference_audio.wav", "ref_text": "这是参考文本", "tts_server": "http://127.0.0.1:9880", "batch_size": 16, "fps": 50, "W": 450, "H": 450, "customvideo_config": ""}, "cosyvoice_template": {"avatar_id": "avator_1", "model": "musetalk", "tts": "cosyvoice", "voice": "reference_audio.wav", "ref_text": "这是参考文本", "tts_server": "http://127.0.0.1:9880", "batch_size": 16, "fps": 50, "W": 450, "H": 450, "customvideo_config": ""}}, "voice_options": {"edgetts": ["zh-CN-XiaoxiaoNeural", "zh-CN-YunxiNeural", "zh-CN-YunjianNeural", "zh-CN-YunxiaNeural", "zh-CN-YunyangNeural", "zh-CN-liaoning-XiaobeiNeural", "zh-CN-shaanxi-XiaoniNeural", "en-US-AriaNeural", "en-US-<PERSON><PERSON><PERSON><PERSON>", "en-US-GuyN<PERSON><PERSON>"], "tencent": ["101001", "101002", "101003", "101004", "101005", "101006", "101007", "101008", "101009", "101010"]}, "model_options": ["musetalk", "wav2lip", "ultralight"], "tts_options": ["edgetts", "gpt-sovits", "xtts", "cosyvoice", "fishtts", "tencent", "do<PERSON>o"]}