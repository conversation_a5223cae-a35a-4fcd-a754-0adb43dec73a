{"asr_config_samples": {"description": "ASR配置示例 - 支持多种语音识别服务", "configs": {"funasr_config": {"description": "FunASR WebSocket配置 (默认)", "config": {"provider": "funasr", "enabled": true, "language": "zh", "sample_rate": 16000, "format": "pcm", "funasr_websocket_url": "wss://www.funasr.com:10096/", "funasr_mode": "2pass", "funasr_use_itn": true, "funasr_hotwords": null}}, "openai_whisper_config": {"description": "OpenAI Whisper API配置", "config": {"provider": "openai_whisper", "enabled": true, "language": "zh", "sample_rate": 16000, "format": "wav", "openai_api_key": "your-openai-api-key", "openai_base_url": "https://api.openai.com/v1", "openai_model": "whisper-1", "openai_temperature": 0.0, "openai_language": "zh"}}, "sensevoice_config": {"description": "SenseVoice模型配置 (自部署)", "config": {"provider": "sensevoice", "enabled": true, "language": "zh", "sample_rate": 16000, "format": "wav", "sensevoice_api_url": "http://your-server:8000", "sensevoice_model": "sensevoice-small", "sensevoice_language": "auto", "sensevoice_use_itn": true}}, "azure_speech_config": {"description": "Azure Speech服务配置", "config": {"provider": "azure_speech", "enabled": true, "language": "zh-CN", "sample_rate": 16000, "format": "wav", "azure_speech_key": "your-azure-speech-key", "azure_speech_region": "eastasia", "azure_speech_language": "zh-CN"}}, "tencent_asr_config": {"description": "腾讯云ASR配置", "config": {"provider": "tencent_asr", "enabled": true, "language": "zh", "sample_rate": 16000, "format": "wav", "tencent_secret_id": "your-tencent-secret-id", "tencent_secret_key": "your-tencent-secret-key", "tencent_app_id": "your-tencent-app-id", "tencent_engine_model_type": "16k_zh"}}, "baidu_asr_config": {"description": "百度ASR配置", "config": {"provider": "baidu_asr", "enabled": true, "language": "zh", "sample_rate": 16000, "format": "wav", "baidu_app_id": "your-baidu-app-id", "baidu_api_key": "your-baidu-api-key", "baidu_secret_key": "your-baidu-secret-key"}}}}, "deployment_examples": {"sensevoice_docker": {"description": "SenseVoice Docker部署示例", "docker_command": "docker run -d -p 8000:8000 --name sensevoice-server sensevoice:latest", "api_endpoint": "http://localhost:8000/v1/audio/transcriptions", "test_curl": "curl -X POST http://localhost:8000/v1/audio/transcriptions -F 'audio=@test.wav' -F 'model=sensevoice-small'"}, "openai_compatible": {"description": "OpenAI兼容接口部署", "examples": [{"name": "LocalAI", "base_url": "http://localhost:8080/v1", "model": "whisper-1"}, {"name": "Faster-Whisper", "base_url": "http://localhost:8000/v1", "model": "large-v3"}, {"name": "Whisper.cpp", "base_url": "http://localhost:8080/v1", "model": "ggml-large-v3"}]}}, "configuration_guide": {"step1": "选择ASR提供商", "step2": "配置相应的API密钥或服务地址", "step3": "设置语言和音频格式参数", "step4": "测试配置是否正常工作", "step5": "在应用中启用新的ASR配置", "notes": ["FunASR: 实时WebSocket流式识别，延迟最低", "OpenAI Whisper: 准确度高，支持多语言", "SenseVoice: 阿里开源模型，可自部署", "Azure Speech: 微软云服务，稳定性好", "腾讯云ASR: 国内服务，中文识别优秀", "百度ASR: 百度云服务，中文支持完善"]}, "environment_variables": {"description": "环境变量配置映射", "variables": {"ASR_PROVIDER": "asr服务提供商", "ASR_ENABLED": "是否启用ASR", "OPENAI_API_KEY": "OpenAI API密钥", "OPENAI_BASE_URL": "OpenAI API基础URL", "SENSEVOICE_API_URL": "SenseVoice服务地址", "AZURE_SPEECH_KEY": "Azure Speech密钥", "AZURE_SPEECH_REGION": "Azure Speech区域", "TENCENT_SECRET_ID": "腾讯云Secret ID", "TENCENT_SECRET_KEY": "腾讯云Secret Key", "TENCENT_APP_ID": "腾讯云App ID", "BAIDU_APP_ID": "百度App ID", "BAIDU_API_KEY": "百度API Key", "BAIDU_SECRET_KEY": "百度Secret Key"}}}