"""
vLLM SenseVoice适配器 - 将SenseVoice模型包装为OpenAI兼容的API
"""
import asyncio
import json
import logging
import base64
import io
import wave
import tempfile
import os
from typing import Dict, Any, Optional
import numpy as np
import torch
import torchaudio
from aiohttp import web
import aiofiles

# 假设你已经安装了SenseVoice相关依赖
try:
    from funasr import AutoModel
    SENSEVOICE_AVAILABLE = True
except ImportError:
    SENSEVOICE_AVAILABLE = False
    logging.warning("FunASR未安装，将使用模拟模式")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SenseVoiceModel:
    """SenseVoice模型包装器"""
    
    def __init__(self, model_path: str = "iic/SenseVoiceSmall"):
        self.model_path = model_path
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"使用设备: {self.device}")
    
    async def load_model(self):
        """异步加载模型"""
        try:
            if SENSEVOICE_AVAILABLE:
                # 在线加载模型
                self.model = AutoModel(
                    model=self.model_path,
                    trust_remote_code=True,
                    device=self.device
                )
                logger.info(f"SenseVoice模型加载成功: {self.model_path}")
            else:
                # 模拟模式
                logger.warning("使用模拟模式，请安装FunASR")
                self.model = "mock_model"
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    async def transcribe(self, audio_data: bytes, language: str = "auto", use_itn: bool = True) -> str:
        """转录音频"""
        try:
            if not SENSEVOICE_AVAILABLE:
                # 模拟转录结果
                return "这是模拟的转录结果，请安装FunASR以使用真实模型"
            
            # 保存临时音频文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name
            
            try:
                # 使用SenseVoice进行转录
                result = self.model.generate(
                    input=temp_path,
                    cache={},
                    language=language,
                    use_itn=use_itn,
                    batch_size_s=60,
                    merge_vad=True,
                    merge_length_s=15
                )
                
                # 提取文本结果
                if isinstance(result, list) and len(result) > 0:
                    text = result[0].get('text', '')
                    return text.strip()
                else:
                    return ""
                    
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except Exception as e:
            logger.error(f"转录失败: {e}")
            return ""

class VLLMSenseVoiceServer:
    """vLLM兼容的SenseVoice服务器"""
    
    def __init__(self, model_path: str = "iic/SenseVoiceSmall"):
        self.model = SenseVoiceModel(model_path)
        self.model_name = "sensevoice-small"
    
    async def initialize(self):
        """初始化服务器"""
        await self.model.load_model()
        logger.info("vLLM SenseVoice服务器初始化完成")
    
    async def transcribe_audio(self, request):
        """音频转录API - OpenAI兼容格式"""
        try:
            # 解析请求
            if request.content_type == 'application/json':
                # JSON格式请求
                data = await request.json()
                audio_b64 = data.get('audio', '')
                language = data.get('language', 'auto')
                use_itn = data.get('use_itn', True)
                
                if not audio_b64:
                    return web.json_response({"error": "缺少音频数据"}, status=400)
                
                # 解码base64音频
                try:
                    audio_data = base64.b64decode(audio_b64)
                except Exception as e:
                    return web.json_response({"error": f"音频解码失败: {e}"}, status=400)
            
            else:
                # multipart/form-data格式请求
                reader = await request.multipart()
                audio_data = None
                language = 'auto'
                use_itn = True
                
                async for field in reader:
                    if field.name == 'file' or field.name == 'audio':
                        audio_data = await field.read()
                    elif field.name == 'language':
                        language = await field.text()
                    elif field.name == 'use_itn':
                        use_itn = (await field.text()).lower() == 'true'
                
                if not audio_data:
                    return web.json_response({"error": "未找到音频文件"}, status=400)
            
            # 执行转录
            text = await self.model.transcribe(audio_data, language, use_itn)
            
            # 返回OpenAI兼容格式
            response = {
                "text": text,
                "task": "transcribe",
                "language": language,
                "duration": 0.0,  # 可以计算实际时长
                "segments": [
                    {
                        "id": 0,
                        "seek": 0,
                        "start": 0.0,
                        "end": 0.0,
                        "text": text,
                        "tokens": [],
                        "temperature": 0.0,
                        "avg_logprob": 0.0,
                        "compression_ratio": 0.0,
                        "no_speech_prob": 0.0
                    }
                ]
            }
            
            return web.json_response(response)
            
        except Exception as e:
            logger.error(f"转录请求处理失败: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def list_models(self, request):
        """列出可用模型"""
        models = [
            {
                "id": self.model_name,
                "object": "model",
                "created": **********,
                "owned_by": "sensevoice",
                "permission": [],
                "root": self.model_name,
                "parent": None
            }
        ]
        
        return web.json_response({
            "object": "list",
            "data": models
        })
    
    async def health_check(self, request):
        """健康检查"""
        return web.json_response({
            "status": "healthy",
            "model": self.model_name,
            "device": self.model.device,
            "sensevoice_available": SENSEVOICE_AVAILABLE
        })
    
    async def model_info(self, request):
        """模型信息"""
        model_id = request.match_info.get('model_id', self.model_name)
        
        if model_id != self.model_name:
            return web.json_response({"error": "模型不存在"}, status=404)
        
        return web.json_response({
            "id": self.model_name,
            "object": "model",
            "created": **********,
            "owned_by": "sensevoice",
            "permission": [],
            "root": self.model_name,
            "parent": None,
            "capabilities": {
                "transcription": True,
                "translation": False,
                "languages": ["zh", "en", "ja", "ko", "auto"],
                "max_audio_length": 30
            }
        })

def create_vllm_app(model_path: str = "iic/SenseVoiceSmall") -> web.Application:
    """创建vLLM兼容应用"""
    app = web.Application(client_max_size=100*1024*1024)  # 100MB max file size
    
    # 创建服务器实例
    server = VLLMSenseVoiceServer(model_path)
    
    # 设置路由 - OpenAI兼容
    app.router.add_post('/v1/audio/transcriptions', server.transcribe_audio)
    app.router.add_get('/v1/models', server.list_models)
    app.router.add_get('/v1/models/{model_id}', server.model_info)
    app.router.add_get('/health', server.health_check)
    
    # 兼容路由
    app.router.add_post('/audio/transcriptions', server.transcribe_audio)
    app.router.add_get('/models', server.list_models)
    
    # 存储服务器实例以便初始化
    app['server'] = server
    
    return app

async def init_app(app):
    """初始化应用"""
    server = app['server']
    await server.initialize()

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='vLLM SenseVoice服务器')
    parser.add_argument('--model', default='iic/SenseVoiceSmall', help='模型路径')
    parser.add_argument('--host', default='0.0.0.0', help='服务器地址')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口')
    
    args = parser.parse_args()
    
    # 创建应用
    app = create_vllm_app(args.model)
    
    # 初始化
    await init_app(app)
    
    # 启动服务器
    runner = web.AppRunner(app)
    await runner.setup()
    
    site = web.TCPSite(runner, args.host, args.port)
    await site.start()
    
    logger.info(f"vLLM SenseVoice服务器启动在 http://{args.host}:{args.port}")
    logger.info(f"模型: {args.model}")
    logger.info(f"API端点: http://{args.host}:{args.port}/v1/audio/transcriptions")
    logger.info(f"健康检查: http://{args.host}:{args.port}/health")
    
    # 保持服务运行
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("服务器停止")
    finally:
        await runner.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
