"""
增强版ASR处理器 - 集成多种ASR服务到LiveTalking应用
支持FunASR、OpenAI Whisper、SenseVoice等
"""
import asyncio
import json
import logging
import base64
import io
import wave
from typing import Optional, Dict, Any
from aiohttp import web, WSMsgType
import aiohttp

from asr_config_manager import (
    ASRConfigManager,
    get_asr_config_manager,
    init_asr_config_manager,
    ASRProvider
)

logger = logging.getLogger(__name__)

class EnhancedASRHandler:
    """增强版ASR处理器"""
    
    def __init__(self, asr_config_manager: ASRConfigManager):
        self.asr_config_manager = asr_config_manager
        self.active_sessions: Dict[str, Dict] = {}
    
    async def handle_websocket(self, request):
        """处理WebSocket ASR请求"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        session_id = request.query.get('session_id', 'default')
        self.active_sessions[session_id] = {
            'ws': ws,
            'audio_buffer': b'',
            'config': self.asr_config_manager.config
        }
        
        logger.info(f"ASR WebSocket连接建立: {session_id}")
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    await self._handle_text_message(session_id, msg.data)
                elif msg.type == WSMsgType.BINARY:
                    await self._handle_audio_data(session_id, msg.data)
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'ASR WebSocket错误: {ws.exception()}')
                    break
        except Exception as e:
            logger.error(f"ASR WebSocket处理错误: {e}")
        finally:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            logger.info(f"ASR WebSocket连接关闭: {session_id}")
        
        return ws
    
    async def _handle_text_message(self, session_id: str, message: str):
        """处理文本消息"""
        try:
            data = json.loads(message)
            command = data.get('command')
            
            if command == 'start':
                await self._start_recognition(session_id, data)
            elif command == 'stop':
                await self._stop_recognition(session_id, data)
            elif command == 'config':
                await self._update_config(session_id, data)
            else:
                logger.warning(f"未知命令: {command}")
                
        except json.JSONDecodeError:
            logger.error(f"无效的JSON消息: {message}")
    
    async def _handle_audio_data(self, session_id: str, audio_data: bytes):
        """处理音频数据"""
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        config = session['config']
        
        # 根据ASR提供商处理音频
        provider = ASRProvider(config.provider)
        
        if provider == ASRProvider.FUNASR:
            # FunASR使用原有的WebSocket流式处理
            await self._handle_funasr_audio(session_id, audio_data)
        else:
            # HTTP API类型的ASR服务
            await self._handle_http_asr_audio(session_id, audio_data)
    
    async def _handle_funasr_audio(self, session_id: str, audio_data: bytes):
        """处理FunASR音频数据"""
        # 这里保持原有的FunASR WebSocket处理逻辑
        # 或者转发到FunASR服务器
        session = self.active_sessions[session_id]
        ws = session['ws']
        
        # 模拟FunASR响应格式
        response = {
            "text": "FunASR处理中...",
            "mode": "online",
            "is_final": False,
            "timestamp": []
        }
        
        await ws.send_text(json.dumps(response))
    
    async def _handle_http_asr_audio(self, session_id: str, audio_data: bytes):
        """处理HTTP API类型的ASR音频数据"""
        session = self.active_sessions[session_id]
        ws = session['ws']
        
        # 累积音频数据
        session['audio_buffer'] += audio_data
        
        # 当累积足够的音频数据时进行识别
        if len(session['audio_buffer']) >= 32000:  # 约2秒的16kHz PCM数据
            try:
                # 转换PCM为WAV格式
                wav_data = self._pcm_to_wav(session['audio_buffer'])
                
                # 调用ASR服务
                text = await self.asr_config_manager.transcribe_audio(wav_data, "wav")
                
                if text:
                    # 发送识别结果
                    response = {
                        "text": text,
                        "mode": "http_api",
                        "is_final": True,
                        "timestamp": []
                    }
                    await ws.send_text(json.dumps(response))
                
                # 清空缓冲区
                session['audio_buffer'] = b''
                
            except Exception as e:
                logger.error(f"HTTP ASR处理失败: {e}")
    
    def _pcm_to_wav(self, pcm_data: bytes, sample_rate: int = 16000, channels: int = 1, sample_width: int = 2) -> bytes:
        """将PCM数据转换为WAV格式"""
        wav_buffer = io.BytesIO()
        
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(channels)
            wav_file.setsampwidth(sample_width)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(pcm_data)
        
        wav_buffer.seek(0)
        return wav_buffer.read()
    
    async def _start_recognition(self, session_id: str, data: Dict[str, Any]):
        """开始识别"""
        logger.info(f"开始ASR识别: {session_id}")
        
        session = self.active_sessions[session_id]
        ws = session['ws']
        
        response = {
            "status": "started",
            "provider": session['config'].provider,
            "message": "ASR识别已开始"
        }
        await ws.send_text(json.dumps(response))
    
    async def _stop_recognition(self, session_id: str, data: Dict[str, Any]):
        """停止识别"""
        logger.info(f"停止ASR识别: {session_id}")
        
        session = self.active_sessions[session_id]
        ws = session['ws']
        
        # 处理剩余的音频数据
        if session['audio_buffer']:
            try:
                wav_data = self._pcm_to_wav(session['audio_buffer'])
                text = await self.asr_config_manager.transcribe_audio(wav_data, "wav")
                
                if text:
                    response = {
                        "text": text,
                        "mode": "final",
                        "is_final": True,
                        "timestamp": []
                    }
                    await ws.send_text(json.dumps(response))
                
            except Exception as e:
                logger.error(f"最终ASR处理失败: {e}")
            
            session['audio_buffer'] = b''
        
        response = {
            "status": "stopped",
            "message": "ASR识别已停止"
        }
        await ws.send_text(json.dumps(response))
    
    async def _update_config(self, session_id: str, data: Dict[str, Any]):
        """更新配置"""
        try:
            config_updates = data.get('config', {})
            
            # 更新会话级配置
            if session_id in self.active_sessions:
                session = self.active_sessions[session_id]
                for key, value in config_updates.items():
                    if hasattr(session['config'], key):
                        setattr(session['config'], key, value)
            
            logger.info(f"更新ASR配置: {session_id} - {config_updates}")
            
        except Exception as e:
            logger.error(f"更新ASR配置失败: {e}")

class ASRAPIHandler:
    """ASR API处理器 - 提供REST API接口"""
    
    def __init__(self, asr_config_manager: ASRConfigManager):
        self.asr_config_manager = asr_config_manager
    
    async def transcribe_file(self, request):
        """文件转录API"""
        try:
            reader = await request.multipart()
            audio_file = None
            
            async for field in reader:
                if field.name == 'audio':
                    audio_data = await field.read()
                    audio_file = audio_data
                    break
            
            if not audio_file:
                return web.json_response({"error": "未找到音频文件"}, status=400)
            
            # 转录音频
            text = await self.asr_config_manager.transcribe_audio(audio_file, "wav")
            
            return web.json_response({
                "text": text,
                "provider": self.asr_config_manager.config.provider,
                "success": True
            })
            
        except Exception as e:
            logger.error(f"文件转录失败: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def get_config(self, request):
        """获取ASR配置"""
        try:
            config = self.asr_config_manager.config.to_dict()
            
            # 隐藏敏感信息
            sensitive_keys = ['api_key', 'secret_key', 'secret_id']
            for key in config:
                if any(sensitive in key.lower() for sensitive in sensitive_keys):
                    if config[key]:
                        config[key] = "***"
            
            return web.json_response({
                "config": config,
                "providers": self.asr_config_manager.get_supported_providers()
            })
            
        except Exception as e:
            logger.error(f"获取ASR配置失败: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def update_config(self, request):
        """更新ASR配置"""
        try:
            data = await request.json()
            config_updates = data.get('config', {})
            
            # 更新配置
            self.asr_config_manager.update_config(**config_updates)
            
            return web.json_response({
                "message": "ASR配置更新成功",
                "success": True
            })
            
        except Exception as e:
            logger.error(f"更新ASR配置失败: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def test_config(self, request):
        """测试ASR配置"""
        try:
            # 这里可以添加配置测试逻辑
            # 比如发送测试音频到ASR服务
            
            return web.json_response({
                "message": "ASR配置测试成功",
                "provider": self.asr_config_manager.config.provider,
                "success": True
            })
            
        except Exception as e:
            logger.error(f"ASR配置测试失败: {e}")
            return web.json_response({"error": str(e)}, status=500)

def setup_asr_routes(app: web.Application, asr_config_manager: ASRConfigManager):
    """设置ASR路由"""
    
    # WebSocket处理器
    asr_handler = EnhancedASRHandler(asr_config_manager)
    
    # API处理器
    api_handler = ASRAPIHandler(asr_config_manager)
    
    # 添加路由
    app.router.add_get('/asr/ws', asr_handler.handle_websocket)
    app.router.add_post('/asr/transcribe', api_handler.transcribe_file)
    app.router.add_get('/asr/config', api_handler.get_config)
    app.router.add_post('/asr/config', api_handler.update_config)
    app.router.add_post('/asr/test', api_handler.test_config)
    
    logger.info("ASR路由设置完成")

async def init_enhanced_asr(app: web.Application, config_file: str = "asr_config.json"):
    """初始化增强版ASR系统"""
    try:
        # 初始化ASR配置管理器
        asr_config_manager = init_asr_config_manager(config_file)
        
        # 设置路由
        setup_asr_routes(app, asr_config_manager)
        
        logger.info("增强版ASR系统初始化完成")
        return asr_config_manager
        
    except Exception as e:
        logger.error(f"初始化增强版ASR系统失败: {e}")
        raise
