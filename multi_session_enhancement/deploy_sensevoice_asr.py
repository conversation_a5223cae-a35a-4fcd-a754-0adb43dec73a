"""
SenseVoice ASR服务部署脚本
支持多种部署方式：Docker、直接部署、vLLM集成
"""
import asyncio
import json
import logging
import os
import subprocess
import sys
from pathlib import Path
from typing import Dict, Any, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SenseVoiceDeployer:
    """SenseVoice ASR部署器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.config_file = self.base_dir / "sensevoice_config.json"
    
    def create_config(self, 
                     vllm_url: str = "http://localhost:8000",
                     asr_port: int = 10096,
                     model_name: str = "sensevoice-small") -> Dict[str, Any]:
        """创建配置文件"""
        config = {
            "vllm_service": {
                "base_url": vllm_url,
                "model_name": model_name,
                "timeout": 30.0
            },
            "asr_service": {
                "host": "0.0.0.0",
                "port": asr_port,
                "max_audio_length": 30,
                "chunk_size": 960,
                "sample_rate": 16000
            },
            "model_config": {
                "language": "auto",
                "use_itn": True,
                "batch_size": 1,
                "device": "cuda"
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            }
        }
        
        # 保存配置
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"配置文件已创建: {self.config_file}")
        return config
    
    def create_docker_compose(self, config: Dict[str, Any]):
        """创建Docker Compose文件"""
        docker_compose = f"""version: '3.8'

services:
  # vLLM SenseVoice服务
  vllm-sensevoice:
    image: vllm/vllm-openai:latest
    container_name: vllm-sensevoice
    ports:
      - "8000:8000"
    volumes:
      - ./models:/models
      - ./cache:/cache
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - HF_HOME=/cache
    command: >
      --model iic/SenseVoiceSmall
      --served-model-name sensevoice-small
      --host 0.0.0.0
      --port 8000
      --trust-remote-code
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # SenseVoice ASR服务
  sensevoice-asr:
    build:
      context: .
      dockerfile: Dockerfile.sensevoice
    container_name: sensevoice-asr
    ports:
      - "{config['asr_service']['port']}:{config['asr_service']['port']}"
    environment:
      - VLLM_BASE_URL=http://vllm-sensevoice:8000
      - ASR_PORT={config['asr_service']['port']}
      - MODEL_NAME={config['vllm_service']['model_name']}
    depends_on:
      - vllm-sensevoice
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:{config['asr_service']['port']}/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  default:
    name: sensevoice-network
"""
        
        compose_file = self.base_dir / "docker-compose.yml"
        with open(compose_file, 'w') as f:
            f.write(docker_compose)
        
        logger.info(f"Docker Compose文件已创建: {compose_file}")
    
    def create_dockerfile(self):
        """创建Dockerfile"""
        dockerfile_content = """FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    curl \\
    wget \\
    git \\
    build-essential \\
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 10096

# 启动命令
CMD ["python", "sensevoice_asr_server.py"]
"""
        
        dockerfile = self.base_dir / "Dockerfile.sensevoice"
        with open(dockerfile, 'w') as f:
            f.write(dockerfile_content)
        
        logger.info(f"Dockerfile已创建: {dockerfile}")
    
    def create_requirements(self):
        """创建requirements.txt"""
        requirements = """aiohttp>=3.8.0
aiofiles>=0.8.0
numpy>=1.21.0
torch>=1.12.0
torchaudio>=0.12.0
funasr>=1.0.0
websockets>=10.0
requests>=2.28.0
"""
        
        req_file = self.base_dir / "requirements.txt"
        with open(req_file, 'w') as f:
            f.write(requirements)
        
        logger.info(f"Requirements文件已创建: {req_file}")
    
    def create_startup_script(self, config: Dict[str, Any]):
        """创建启动脚本"""
        startup_script = f"""#!/bin/bash

# SenseVoice ASR服务启动脚本

set -e

echo "启动SenseVoice ASR服务..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: Python3未安装"
    exit 1
fi

# 检查依赖
echo "检查Python依赖..."
python3 -c "import aiohttp, numpy, torch, torchaudio" || {{
    echo "错误: 缺少必要的Python依赖，请运行: pip install -r requirements.txt"
    exit 1
}}

# 设置环境变量
export VLLM_BASE_URL="{config['vllm_service']['base_url']}"
export ASR_PORT="{config['asr_service']['port']}"
export MODEL_NAME="{config['vllm_service']['model_name']}"

# 启动服务
echo "启动ASR服务在端口 {config['asr_service']['port']}..."
python3 sensevoice_asr_server.py \\
    --vllm-url "$VLLM_BASE_URL" \\
    --port "$ASR_PORT" \\
    --model "$MODEL_NAME"
"""
        
        script_file = self.base_dir / "start_asr.sh"
        with open(script_file, 'w') as f:
            f.write(startup_script)
        
        # 添加执行权限
        os.chmod(script_file, 0o755)
        
        logger.info(f"启动脚本已创建: {script_file}")
    
    def create_systemd_service(self, config: Dict[str, Any]):
        """创建systemd服务文件"""
        service_content = f"""[Unit]
Description=SenseVoice ASR Service
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory={self.base_dir.absolute()}
Environment=VLLM_BASE_URL={config['vllm_service']['base_url']}
Environment=ASR_PORT={config['asr_service']['port']}
Environment=MODEL_NAME={config['vllm_service']['model_name']}
ExecStart=/usr/bin/python3 {self.base_dir.absolute()}/sensevoice_asr_server.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
        
        service_file = self.base_dir / "sensevoice-asr.service"
        with open(service_file, 'w') as f:
            f.write(service_content)
        
        logger.info(f"Systemd服务文件已创建: {service_file}")
        logger.info("要安装服务，请运行:")
        logger.info(f"sudo cp {service_file} /etc/systemd/system/")
        logger.info("sudo systemctl daemon-reload")
        logger.info("sudo systemctl enable sensevoice-asr")
        logger.info("sudo systemctl start sensevoice-asr")
    
    def test_deployment(self, config: Dict[str, Any]):
        """测试部署"""
        import requests
        import time
        
        asr_url = f"http://localhost:{config['asr_service']['port']}"
        vllm_url = config['vllm_service']['base_url']
        
        logger.info("测试部署...")
        
        # 等待服务启动
        time.sleep(5)
        
        try:
            # 测试vLLM服务
            logger.info("测试vLLM服务...")
            response = requests.get(f"{vllm_url}/health", timeout=10)
            if response.status_code == 200:
                logger.info("✅ vLLM服务正常")
            else:
                logger.warning(f"⚠️ vLLM服务响应异常: {response.status_code}")
        except Exception as e:
            logger.error(f"❌ vLLM服务连接失败: {e}")
        
        try:
            # 测试ASR服务
            logger.info("测试ASR服务...")
            response = requests.get(f"{asr_url}/health", timeout=10)
            if response.status_code == 200:
                logger.info("✅ ASR服务正常")
                logger.info(f"服务信息: {response.json()}")
            else:
                logger.warning(f"⚠️ ASR服务响应异常: {response.status_code}")
        except Exception as e:
            logger.error(f"❌ ASR服务连接失败: {e}")
    
    def deploy(self, 
               vllm_url: str = "http://localhost:8000",
               asr_port: int = 10096,
               model_name: str = "sensevoice-small",
               deployment_type: str = "standalone"):
        """执行部署"""
        logger.info(f"开始部署SenseVoice ASR服务 (类型: {deployment_type})")
        
        # 创建配置
        config = self.create_config(vllm_url, asr_port, model_name)
        
        # 创建必要文件
        self.create_requirements()
        
        if deployment_type == "docker":
            self.create_dockerfile()
            self.create_docker_compose(config)
            logger.info("Docker部署文件已创建，运行: docker-compose up -d")
        
        elif deployment_type == "systemd":
            self.create_startup_script(config)
            self.create_systemd_service(config)
            logger.info("Systemd服务文件已创建")
        
        else:  # standalone
            self.create_startup_script(config)
            logger.info("独立部署文件已创建，运行: ./start_asr.sh")
        
        logger.info("部署文件创建完成！")
        
        # 显示下一步操作
        self.show_next_steps(deployment_type, config)
    
    def show_next_steps(self, deployment_type: str, config: Dict[str, Any]):
        """显示下一步操作"""
        logger.info("\\n" + "="*50)
        logger.info("下一步操作:")
        logger.info("="*50)
        
        if deployment_type == "docker":
            logger.info("1. 确保Docker和Docker Compose已安装")
            logger.info("2. 运行: docker-compose up -d")
            logger.info("3. 检查服务状态: docker-compose ps")
        
        elif deployment_type == "systemd":
            logger.info("1. 安装Python依赖: pip install -r requirements.txt")
            logger.info("2. 安装systemd服务:")
            logger.info(f"   sudo cp sensevoice-asr.service /etc/systemd/system/")
            logger.info("   sudo systemctl daemon-reload")
            logger.info("   sudo systemctl enable sensevoice-asr")
            logger.info("   sudo systemctl start sensevoice-asr")
        
        else:  # standalone
            logger.info("1. 安装Python依赖: pip install -r requirements.txt")
            logger.info("2. 启动vLLM服务 (如果还未启动)")
            logger.info("3. 运行ASR服务: ./start_asr.sh")
        
        logger.info("\\n服务地址:")
        logger.info(f"- ASR WebSocket: ws://localhost:{config['asr_service']['port']}/ws")
        logger.info(f"- ASR HTTP API: http://localhost:{config['asr_service']['port']}/v1/audio/transcriptions")
        logger.info(f"- 健康检查: http://localhost:{config['asr_service']['port']}/health")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SenseVoice ASR服务部署工具')
    parser.add_argument('--vllm-url', default='http://localhost:8000', help='vLLM服务地址')
    parser.add_argument('--asr-port', type=int, default=10096, help='ASR服务端口')
    parser.add_argument('--model-name', default='sensevoice-small', help='模型名称')
    parser.add_argument('--type', choices=['standalone', 'docker', 'systemd'], 
                       default='standalone', help='部署类型')
    parser.add_argument('--test', action='store_true', help='测试现有部署')
    
    args = parser.parse_args()
    
    deployer = SenseVoiceDeployer()
    
    if args.test:
        # 加载配置并测试
        if deployer.config_file.exists():
            with open(deployer.config_file, 'r') as f:
                config = json.load(f)
            deployer.test_deployment(config)
        else:
            logger.error("配置文件不存在，请先运行部署")
    else:
        # 执行部署
        deployer.deploy(
            vllm_url=args.vllm_url,
            asr_port=args.asr_port,
            model_name=args.model_name,
            deployment_type=args.type
        )

if __name__ == "__main__":
    main()
