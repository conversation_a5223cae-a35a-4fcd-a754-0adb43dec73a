# 多会话形象声音配置增强

## 概述
此目录包含对LiveTalking项目的多会话增强功能，允许不同会话使用不同的形象和声音配置。

## 功能特性
1. **会话级配置**：每个会话可以独立配置形象ID、TTS类型、声音参数等
2. **动态资源加载**：支持运行时动态加载不同的avatar和模型
3. **配置管理**：提供会话配置的增删改查接口
4. **资源优化**：智能缓存机制，避免重复加载相同资源

## 文件结构
```
multi_session_enhancement/
├── README.md                    # 说明文档
├── session_manager.py          # 会话管理器
├── config_manager.py           # 配置管理器
├── resource_manager.py         # 资源管理器
├── enhanced_app.py             # 增强版应用入口
├── api_extensions.py           # API扩展
├── nacos_config_manager.py     # Nacos配置管理器
├── configs/                    # 配置文件目录
│   ├── session_templates.json  # 会话模板配置
│   ├── avatar_configs.json     # 形象配置
│   └── nacos_app_config.yaml   # Nacos应用配置
└── examples/                   # 使用示例
    ├── multi_avatar_demo.py    # 多形象演示
    ├── session_config_demo.py  # 会话配置演示
    └── nacos_config_demo.py    # Nacos配置演示
```

## 核心改进点

### 1. 会话管理器 (SessionManager)
- 管理多个会话的生命周期
- 为每个会话维护独立的配置
- 支持会话配置的动态更新

### 2. 配置管理器 (ConfigManager)
- 管理会话级别的配置参数
- 支持配置模板和继承
- 提供配置验证和默认值处理

### 3. 资源管理器 (ResourceManager)
- 智能加载和缓存avatar、模型等资源
- 支持资源的引用计数和自动释放
- 优化内存使用

### 4. API扩展
- 扩展现有API支持会话级配置
- 新增会话配置管理接口
- 保持向后兼容性

## 使用方式

### 创建带配置的会话
```python
# POST /offer_with_config
{
    "sdp": "...",
    "type": "offer",
    "session_config": {
        "avatar_id": "avatar_2",
        "model": "musetalk",
        "tts": "edgetts",
        "voice": "zh-CN-XiaoxiaoNeural"
    }
}
```

### 更新会话配置
```python
# POST /update_session_config
{
    "session_id": 123456,
    "config": {
        "voice": "zh-CN-YunxiNeural"
    }
}
```

## 兼容性
- 完全向后兼容现有API
- 现有客户端无需修改即可继续使用
- 新功能通过新的API端点提供

## 部署说明
1. 将此目录复制到LiveTalking项目根目录
2. 安装额外依赖（如有）
3. 使用 `enhanced_app.py` 替代原始 `app.py` 启动服务
4. 或者通过配置开关在原始app中启用增强功能
