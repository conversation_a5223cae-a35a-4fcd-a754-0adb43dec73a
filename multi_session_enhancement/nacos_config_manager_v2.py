"""
Nacos配置管理器 V2 - 支持自动刷新和配置监听
"""
import os
import json
import yaml
import logging
import time
import threading
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass, asdict
from threading import Lock, Thread, Event
from nacos import NacosClient

logger = logging.getLogger(__name__)

@dataclass
class APIConfig:
    """API配置类"""
    # 阿里云DashScope配置
    dashscope_api_key: str = ""
    
    # 腾讯云配置
    tencent_appid: str = ""
    tencent_secret_key: str = ""
    tencent_secret_id: str = ""
    
    # 豆包/火山引擎配置
    doubao_appid: str = ""
    doubao_token: str = ""
    
    # OpenAI配置
    openai_api_key: str = ""
    openai_base_url: str = "https://api.openai.com/v1"
    
    # 其他TTS服务配置
    azure_speech_key: str = ""
    azure_speech_region: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'APIConfig':
        """从字典创建实例"""
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

@dataclass
class LLMConfig:
    """LLM配置类"""
    provider: str = "dashscope"
    api_key: str = ""
    base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    model: str = "qwen-plus"
    max_tokens: int = 8192
    temperature: float = 0.7
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LLMConfig':
        """从字典创建实例"""
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

@dataclass
class TTSConfig:
    """TTS配置类"""
    provider: str = "edgetts"
    voice: str = "zh-CN-YunxiaNeural"
    speed: float = 1.0
    volume: float = 1.0
    pitch: float = 1.0
    
    # 腾讯云TTS特有配置
    app_id: str = ""
    secret_key: str = ""
    secret_id: str = ""
    
    # 豆包TTS特有配置
    api_key: str = ""
    
    # 其他TTS服务配置
    api_url: str = ""
    ref_text: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TTSConfig':
        """从字典创建实例"""
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

class ConfigChangeListener:
    """配置变更监听器"""
    
    def __init__(self, callback: Callable[[str, Dict[str, Any]], None]):
        """
        初始化监听器
        
        Args:
            callback: 配置变更回调函数，参数为(data_id, new_config)
        """
        self.callback = callback
    
    def on_config_change(self, data_id: str, new_config: Dict[str, Any]):
        """配置变更时调用"""
        try:
            self.callback(data_id, new_config)
        except Exception as e:
            logger.error(f"配置变更回调执行失败: {e}")

class NacosConfigManagerV2:
    """Nacos配置管理器 V2 - 支持自动刷新"""
    
    def __init__(self, nacos_client: Optional[NacosClient] = None, 
                 server_addresses: str = "127.0.0.1:8848",
                 namespace: str = "",
                 username: str = "nacos",
                 password: str = "nacos",
                 enable_auto_refresh: bool = True,
                 refresh_interval: int = 30):
        """
        初始化配置管理器
        
        Args:
            nacos_client: Nacos客户端实例
            server_addresses: Nacos服务器地址
            namespace: 命名空间
            username: 用户名
            password: 密码
            enable_auto_refresh: 是否启用自动刷新
            refresh_interval: 刷新间隔（秒）
        """
        if nacos_client:
            self.client = nacos_client
        else:
            try:
                self.client = NacosClient(
                    server_addresses=server_addresses,
                    namespace=namespace,
                    username=username,
                    password=password
                )
                logger.info(f"Nacos客户端初始化成功: {server_addresses}")
            except Exception as e:
                logger.error(f"初始化Nacos客户端失败: {e}")
                self.client = None
        
        # 配置缓存
        self._cache = {}
        self._cache_timeout = 300  # 5分钟缓存
        self._last_update = {}
        self._cache_lock = Lock()
        
        # 配置数据ID
        self.api_config_data_id = "livetalking-api-config"
        self.llm_config_data_id = "livetalking-llm-config"
        self.tts_config_data_id = "livetalking-tts-config"
        self.group = "DEFAULT_GROUP"
        
        # 自动刷新相关
        self.enable_auto_refresh = enable_auto_refresh
        self.refresh_interval = refresh_interval
        self._refresh_thread = None
        self._stop_refresh = Event()
        self._config_listeners = {}  # 配置变更监听器
        self._config_versions = {}   # 配置版本号
        
        # 启动自动刷新
        if self.enable_auto_refresh and self.client:
            self._start_auto_refresh()
    
    def _start_auto_refresh(self):
        """启动自动刷新线程"""
        if self._refresh_thread and self._refresh_thread.is_alive():
            return
        
        self._stop_refresh.clear()
        self._refresh_thread = Thread(target=self._refresh_loop, daemon=True)
        self._refresh_thread.start()
        logger.info(f"配置自动刷新已启动，间隔: {self.refresh_interval}秒")
    
    def _refresh_loop(self):
        """自动刷新循环"""
        while not self._stop_refresh.wait(self.refresh_interval):
            try:
                self._check_config_changes()
            except Exception as e:
                logger.error(f"配置刷新检查失败: {e}")
    
    def _check_config_changes(self):
        """检查配置变更"""
        if not self.client:
            return
        
        config_ids = [
            self.api_config_data_id,
            self.llm_config_data_id,
            self.tts_config_data_id
        ]
        
        for data_id in config_ids:
            try:
                # 获取当前配置
                current_config = self._get_config_from_nacos(data_id)
                if current_config is None:
                    continue
                
                # 检查是否有变更
                cache_key = f"{data_id}:{self.group}"
                old_config = self._cache.get(cache_key)
                
                if old_config != current_config:
                    logger.info(f"检测到配置变更: {data_id}")
                    
                    # 更新缓存
                    with self._cache_lock:
                        self._cache[cache_key] = current_config
                        self._last_update[cache_key] = time.time()
                    
                    # 触发监听器
                    self._notify_config_change(data_id, current_config)
                    
            except Exception as e:
                logger.error(f"检查配置变更失败 {data_id}: {e}")
    
    def _notify_config_change(self, data_id: str, new_config: Dict[str, Any]):
        """通知配置变更"""
        listeners = self._config_listeners.get(data_id, [])
        for listener in listeners:
            try:
                listener.on_config_change(data_id, new_config)
            except Exception as e:
                logger.error(f"配置变更通知失败: {e}")
    
    def add_config_listener(self, data_id: str, listener: ConfigChangeListener):
        """添加配置变更监听器"""
        if data_id not in self._config_listeners:
            self._config_listeners[data_id] = []
        self._config_listeners[data_id].append(listener)
        logger.info(f"已添加配置监听器: {data_id}")
    
    def remove_config_listener(self, data_id: str, listener: ConfigChangeListener):
        """移除配置变更监听器"""
        if data_id in self._config_listeners:
            try:
                self._config_listeners[data_id].remove(listener)
                logger.info(f"已移除配置监听器: {data_id}")
            except ValueError:
                pass
    
    def stop_auto_refresh(self):
        """停止自动刷新"""
        self._stop_refresh.set()
        if self._refresh_thread and self._refresh_thread.is_alive():
            self._refresh_thread.join(timeout=5)
        logger.info("配置自动刷新已停止")
    
    def force_refresh(self):
        """强制刷新所有配置"""
        logger.info("强制刷新所有配置...")
        with self._cache_lock:
            self._cache.clear()
            self._last_update.clear()
        
        # 立即检查配置变更
        self._check_config_changes()
    
    def _get_config_from_nacos(self, data_id: str, group: str = None) -> Optional[Dict[str, Any]]:
        """从Nacos获取配置"""
        if not self.client:
            return None
        
        if group is None:
            group = self.group
            
        try:
            config_content = self.client.get_config(data_id, group)
            if config_content:
                # 尝试解析JSON或YAML
                try:
                    return json.loads(config_content)
                except json.JSONDecodeError:
                    try:
                        return yaml.safe_load(config_content)
                    except yaml.YAMLError:
                        logger.error(f"配置格式错误: {data_id}")
                        return None
            return None
        except Exception as e:
            logger.error(f"从Nacos获取配置失败 {data_id}: {e}")
            return None

    def _get_cached_config(self, data_id: str, group: str = None) -> Optional[Dict[str, Any]]:
        """获取缓存的配置"""
        if group is None:
            group = self.group

        cache_key = f"{data_id}:{group}"

        with self._cache_lock:
            # 检查缓存是否存在且未过期
            if cache_key in self._cache:
                last_update = self._last_update.get(cache_key, 0)
                if time.time() - last_update < self._cache_timeout:
                    return self._cache[cache_key]

        # 缓存不存在或已过期，从Nacos获取
        config = self._get_config_from_nacos(data_id, group)
        if config:
            with self._cache_lock:
                self._cache[cache_key] = config
                self._last_update[cache_key] = time.time()

        return config

    def _get_config_with_fallback(self, data_id: str, config_class, env_mapping: Dict[str, str]) -> Any:
        """获取配置，支持环境变量降级"""
        # 首先尝试从Nacos获取
        config_data = self._get_cached_config(data_id)

        if config_data:
            try:
                return config_class.from_dict(config_data)
            except Exception as e:
                logger.warning(f"解析Nacos配置失败 {data_id}: {e}")

        # 降级到环境变量
        logger.info(f"降级到环境变量: {data_id}")
        env_config = {}
        for config_key, env_key in env_mapping.items():
            env_value = os.getenv(env_key)
            if env_value:
                env_config[config_key] = env_value

        return config_class.from_dict(env_config)

    def get_api_config(self) -> APIConfig:
        """获取API配置"""
        env_mapping = {
            "dashscope_api_key": "DASHSCOPE_API_KEY",
            "tencent_appid": "TENCENT_APPID",
            "tencent_secret_key": "TENCENT_SECRET_KEY",
            "tencent_secret_id": "TENCENT_SECRET_ID",
            "doubao_appid": "DOUBAO_APPID",
            "doubao_token": "DOUBAO_TOKEN",
            "openai_api_key": "OPENAI_API_KEY",
            "openai_base_url": "OPENAI_BASE_URL",
            "azure_speech_key": "AZURE_SPEECH_KEY",
            "azure_speech_region": "AZURE_SPEECH_REGION"
        }

        return self._get_config_with_fallback(
            self.api_config_data_id,
            APIConfig,
            env_mapping
        )

    def get_llm_config(self) -> LLMConfig:
        """获取LLM配置"""
        env_mapping = {
            "api_key": "DASHSCOPE_API_KEY",
            "base_url": "LLM_BASE_URL",
            "model": "LLM_MODEL",
            "provider": "LLM_PROVIDER"
        }

        return self._get_config_with_fallback(
            self.llm_config_data_id,
            LLMConfig,
            env_mapping
        )

    def get_tts_config(self, provider: str) -> TTSConfig:
        """获取TTS配置"""
        # 首先尝试从Nacos获取
        config_data = self._get_cached_config(self.tts_config_data_id)

        if config_data and provider in config_data:
            try:
                return TTSConfig.from_dict(config_data[provider])
            except Exception as e:
                logger.warning(f"解析Nacos TTS配置失败 {provider}: {e}")

        # 降级到环境变量
        logger.info(f"TTS配置降级到环境变量: {provider}")

        if provider == "tencent":
            return TTSConfig(
                provider="tencent",
                app_id=os.getenv("TENCENT_APPID", ""),
                secret_key=os.getenv("TENCENT_SECRET_KEY", ""),
                secret_id=os.getenv("TENCENT_SECRET_ID", ""),
                voice="101001"
            )
        elif provider == "doubao":
            return TTSConfig(
                provider="doubao",
                app_id=os.getenv("DOUBAO_APPID", ""),
                api_key=os.getenv("DOUBAO_TOKEN", ""),
                voice="zh_female_shuangkuaisisi_moon_bigtts"
            )
        else:
            # 默认EdgeTTS配置
            return TTSConfig(
                provider="edgetts",
                voice="zh-CN-YunxiaNeural"
            )

    def publish_config(self, data_id: str, config_data: Dict[str, Any],
                      config_type: str = "json", group: str = None) -> bool:
        """发布配置到Nacos"""
        if not self.client:
            logger.error("Nacos客户端未初始化")
            return False

        if group is None:
            group = self.group

        try:
            # 序列化配置
            if config_type.lower() == "json":
                content = json.dumps(config_data, ensure_ascii=False, indent=2)
            elif config_type.lower() == "yaml":
                content = yaml.dump(config_data, allow_unicode=True, default_flow_style=False)
            else:
                logger.error(f"不支持的配置格式: {config_type}")
                return False

            # 发布到Nacos
            result = self.client.publish_config(data_id, group, content)

            if result:
                logger.info(f"配置发布成功: {data_id}")
                # 清除缓存，强制下次重新获取
                cache_key = f"{data_id}:{group}"
                with self._cache_lock:
                    self._cache.pop(cache_key, None)
                    self._last_update.pop(cache_key, None)
            else:
                logger.error(f"配置发布失败: {data_id}")

            return result
        except Exception as e:
            logger.error(f"发布配置异常 {data_id}: {e}")
            return False

    def clear_cache(self):
        """清除所有缓存"""
        with self._cache_lock:
            self._cache.clear()
            self._last_update.clear()
        logger.info("配置缓存已清除")

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        with self._cache_lock:
            return {
                "cache_size": len(self._cache),
                "cached_configs": list(self._cache.keys()),
                "last_updates": dict(self._last_update),
                "cache_timeout": self._cache_timeout
            }

    def __del__(self):
        """析构函数，停止自动刷新"""
        try:
            self.stop_auto_refresh()
        except:
            pass

# 全局配置管理器实例
_global_config_manager: Optional[NacosConfigManagerV2] = None

def init_config_manager_v2(nacos_client: Optional[NacosClient] = None, **kwargs) -> NacosConfigManagerV2:
    """初始化全局配置管理器V2"""
    global _global_config_manager
    _global_config_manager = NacosConfigManagerV2(nacos_client, **kwargs)
    return _global_config_manager

def get_config_manager_v2() -> Optional[NacosConfigManagerV2]:
    """获取全局配置管理器V2"""
    return _global_config_manager

def get_api_config_v2() -> APIConfig:
    """获取API配置V2"""
    manager = get_config_manager_v2()
    if manager:
        return manager.get_api_config()
    else:
        # 降级到环境变量
        return APIConfig(
            dashscope_api_key=os.getenv("DASHSCOPE_API_KEY", ""),
            tencent_appid=os.getenv("TENCENT_APPID", ""),
            tencent_secret_key=os.getenv("TENCENT_SECRET_KEY", ""),
            tencent_secret_id=os.getenv("TENCENT_SECRET_ID", ""),
            doubao_appid=os.getenv("DOUBAO_APPID", ""),
            doubao_token=os.getenv("DOUBAO_TOKEN", "")
        )

def get_llm_config_v2() -> LLMConfig:
    """获取LLM配置V2"""
    manager = get_config_manager_v2()
    if manager:
        return manager.get_llm_config()
    else:
        return LLMConfig(
            api_key=os.getenv("DASHSCOPE_API_KEY", ""),
            base_url=os.getenv("LLM_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
            model=os.getenv("LLM_MODEL", "qwen-plus")
        )

def get_tts_config_v2(provider: str) -> TTSConfig:
    """获取TTS配置V2"""
    manager = get_config_manager_v2()
    if manager:
        return manager.get_tts_config(provider)
    else:
        if provider == "tencent":
            return TTSConfig(
                provider="tencent",
                app_id=os.getenv("TENCENT_APPID", ""),
                secret_key=os.getenv("TENCENT_SECRET_KEY", ""),
                secret_id=os.getenv("TENCENT_SECRET_ID", ""),
                voice="101001"
            )
        elif provider == "doubao":
            return TTSConfig(
                provider="doubao",
                app_id=os.getenv("DOUBAO_APPID", ""),
                api_key=os.getenv("DOUBAO_TOKEN", ""),
                voice="zh_female_shuangkuaisisi_moon_bigtts"
            )
        else:
            return TTSConfig(provider="edgetts", voice="zh-CN-YunxiaNeural")
