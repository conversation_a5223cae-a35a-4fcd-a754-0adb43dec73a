"""
增强版TTS模块 - 支持Nacos配置管理
替代原始ttsreal.py中的环境变量读取，使用Nacos配置
"""
import os
import sys
import logging
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入原始TTS类
from ttsreal import BaseTTS, TencentTTS, DoubaoTTS
from multi_session_enhancement.nacos_config_manager import get_config_manager, get_tts_config

logger = logging.getLogger(__name__)

class EnhancedTencentTTS(TencentTTS):
    """增强版腾讯TTS，支持Nacos配置"""
    
    def __init__(self, opt, parent):
        # 不调用父类的__init__，避免直接读取环境变量
        BaseTTS.__init__(self, opt, parent)
        
        # 从Nacos配置管理器获取配置
        config_manager = get_config_manager()
        if config_manager:
            try:
                tts_config = config_manager.get_tts_config("tencent")
                self.appid = tts_config.app_id
                self.secret_key = tts_config.secret_key
                self.secret_id = tts_config.secret_id
                
                logger.info("从Nacos获取腾讯TTS配置成功")
            except Exception as e:
                logger.warning(f"从Nacos获取腾讯TTS配置失败: {e}，降级到环境变量")
                # 降级到环境变量
                self.appid = os.getenv("TENCENT_APPID")
                self.secret_key = os.getenv("TENCENT_SECRET_KEY")
                self.secret_id = os.getenv("TENCENT_SECRET_ID")
        else:
            logger.info("配置管理器未初始化，使用环境变量")
            self.appid = os.getenv("TENCENT_APPID")
            self.secret_key = os.getenv("TENCENT_SECRET_KEY")
            self.secret_id = os.getenv("TENCENT_SECRET_ID")
        
        # 验证配置
        if not all([self.appid, self.secret_key, self.secret_id]):
            logger.error("腾讯TTS配置不完整，请检查Nacos配置或环境变量")
        
        # 其他配置保持不变
        self.voice_type = int(opt.REF_FILE)
        self.codec = "pcm"
        self.sample_rate = 16000
        self.volume = 0
        self.speed = 0

class EnhancedDoubaoTTS(DoubaoTTS):
    """增强版豆包TTS，支持Nacos配置"""
    
    def __init__(self, opt, parent):
        # 不调用父类的__init__，避免直接读取环境变量
        BaseTTS.__init__(self, opt, parent)
        
        # 从Nacos配置管理器获取配置
        config_manager = get_config_manager()
        if config_manager:
            try:
                tts_config = config_manager.get_tts_config("doubao")
                self.appid = tts_config.app_id
                self.token = tts_config.api_key
                
                logger.info("从Nacos获取豆包TTS配置成功")
            except Exception as e:
                logger.warning(f"从Nacos获取豆包TTS配置失败: {e}，降级到环境变量")
                # 降级到环境变量
                self.appid = os.getenv("DOUBAO_APPID")
                self.token = os.getenv("DOUBAO_TOKEN")
        else:
            logger.info("配置管理器未初始化，使用环境变量")
            self.appid = os.getenv("DOUBAO_APPID")
            self.token = os.getenv("DOUBAO_TOKEN")
        
        # 验证配置
        if not all([self.appid, self.token]):
            logger.error("豆包TTS配置不完整，请检查Nacos配置或环境变量")
        
        # 其他配置保持不变
        _cluster = 'volcano_tts'
        _host = "openspeech.bytedance.com"
        self.api_url = f"wss://{_host}/api/v1/tts/ws_binary"

        self.request_json = {
            "app": {
                "appid": self.appid,
                "token": "access_token",
                "cluster": _cluster
            },
            "user": {
                "uid": "xxx"
            },
            "audio": {
                "voice_type": "xxx",
                "encoding": "pcm",
                "rate": 16000,
                "speed_ratio": 1.0,
                "volume_ratio": 1.0,
                "pitch_ratio": 1.0,
            },
            "request": {
                "reqid": "xxx",
                "text": "字节跳动语音合成。",
                "text_type": "plain",
                "operation": "xxx"
            }
        }

def create_enhanced_tts(tts_type: str, opt, parent) -> Optional[BaseTTS]:
    """
    创建增强版TTS实例
    
    Args:
        tts_type: TTS类型 (tencent, doubao, edgetts等)
        opt: 配置选项
        parent: 父对象
        
    Returns:
        TTS实例或None
    """
    try:
        if tts_type.lower() == "tencent":
            return EnhancedTencentTTS(opt, parent)
        elif tts_type.lower() == "doubao":
            return EnhancedDoubaoTTS(opt, parent)
        else:
            # 对于其他TTS类型，使用原始实现
            from ttsreal import create_tts
            return create_tts(tts_type, opt, parent)
    except Exception as e:
        logger.error(f"创建TTS实例失败: {tts_type}, 错误: {e}")
        return None

def get_tts_config_for_provider(provider: str) -> dict:
    """
    获取指定TTS提供商的配置
    
    Args:
        provider: TTS提供商名称
        
    Returns:
        配置字典
    """
    config_manager = get_config_manager()
    if config_manager:
        try:
            tts_config = config_manager.get_tts_config(provider)
            return tts_config.to_dict()
        except Exception as e:
            logger.warning(f"从Nacos获取{provider}配置失败: {e}")
    
    # 降级到环境变量
    if provider == "tencent":
        return {
            "app_id": os.getenv("TENCENT_APPID", ""),
            "secret_key": os.getenv("TENCENT_SECRET_KEY", ""),
            "secret_id": os.getenv("TENCENT_SECRET_ID", ""),
            "voice": "101001"
        }
    elif provider == "doubao":
        return {
            "app_id": os.getenv("DOUBAO_APPID", ""),
            "api_key": os.getenv("DOUBAO_TOKEN", ""),
            "voice": "zh_female_shuangkuaisisi_moon_bigtts"
        }
    else:
        return {}

def validate_tts_config(provider: str) -> bool:
    """
    验证TTS配置是否完整
    
    Args:
        provider: TTS提供商名称
        
    Returns:
        配置是否有效
    """
    config = get_tts_config_for_provider(provider)
    
    if provider == "tencent":
        required_fields = ["app_id", "secret_key", "secret_id"]
        return all(config.get(field) for field in required_fields)
    elif provider == "doubao":
        required_fields = ["app_id", "api_key"]
        return all(config.get(field) for field in required_fields)
    elif provider == "edgetts":
        # EdgeTTS不需要API密钥
        return True
    else:
        return False

def list_available_tts_providers() -> list:
    """
    列出可用的TTS提供商
    
    Returns:
        可用提供商列表
    """
    providers = []
    
    # 检查各个提供商的配置
    for provider in ["edgetts", "tencent", "doubao", "gpt-sovits", "cosyvoice", "fishtts"]:
        if validate_tts_config(provider):
            providers.append(provider)
        else:
            logger.debug(f"TTS提供商 {provider} 配置不完整，跳过")
    
    return providers

def test_tts_config():
    """测试TTS配置"""
    print("=== TTS配置测试 ===")
    
    providers = ["edgetts", "tencent", "doubao"]
    
    for provider in providers:
        print(f"\n--- {provider.upper()} ---")
        
        # 测试配置获取
        config = get_tts_config_for_provider(provider)
        print(f"配置: {config}")
        
        # 测试配置验证
        is_valid = validate_tts_config(provider)
        print(f"配置有效性: {'✅ 有效' if is_valid else '❌ 无效'}")
    
    # 列出可用提供商
    available = list_available_tts_providers()
    print(f"\n可用的TTS提供商: {available}")

def migrate_tts_usage_example():
    """TTS使用迁移示例"""
    print("\n=== TTS使用迁移示例 ===")
    
    print("原始代码:")
    print("""
    # 原始代码
    class TencentTTS(BaseTTS):
        def __init__(self, opt, parent):
            super().__init__(opt, parent)
            self.appid = os.getenv("TENCENT_APPID")
            self.secret_key = os.getenv("TENCENT_SECRET_KEY")
            self.secret_id = os.getenv("TENCENT_SECRET_ID")
    """)
    
    print("\n增强后代码:")
    print("""
    # 增强后代码
    class EnhancedTencentTTS(TencentTTS):
        def __init__(self, opt, parent):
            BaseTTS.__init__(self, opt, parent)
            
            # 从Nacos获取配置
            config_manager = get_config_manager()
            if config_manager:
                tts_config = config_manager.get_tts_config("tencent")
                self.appid = tts_config.app_id
                self.secret_key = tts_config.secret_key
                self.secret_id = tts_config.secret_id
            else:
                # 降级到环境变量
                self.appid = os.getenv("TENCENT_APPID")
                self.secret_key = os.getenv("TENCENT_SECRET_KEY")
                self.secret_id = os.getenv("TENCENT_SECRET_ID")
    """)

if __name__ == "__main__":
    # 测试配置
    test_tts_config()
    
    # 显示迁移示例
    migrate_tts_usage_example()
