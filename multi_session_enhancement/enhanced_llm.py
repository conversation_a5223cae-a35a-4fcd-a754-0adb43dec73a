"""
增强版LLM模块 - 支持Nacos配置管理
替代原始的llm.py，使用Nacos配置而不是环境变量
"""
import os
import time
import logging
from typing import Optional

# 导入原始模块
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from basereal import BaseReal
from multi_session_enhancement.nacos_config_manager import get_config_manager, get_llm_config

logger = logging.getLogger(__name__)

def llm_response(message: str, nerfreal: BaseReal):
    """
    增强版LLM响应函数，支持Nacos配置
    
    Args:
        message: 用户输入的消息
        nerfreal: BaseReal实例
    """
    start = time.perf_counter()
    
    try:
        from openai import OpenAI
    except ImportError:
        logger.error("OpenAI库未安装，请运行: pip install openai")
        nerfreal.put_msg_txt("抱歉，语言模型组件未正确安装。")
        return

    # 从Nacos配置管理器获取LLM配置
    config_manager = get_config_manager()
    if config_manager:
        try:
            llm_config = config_manager.get_llm_config()
            api_key = llm_config.api_key
            base_url = llm_config.base_url
            model = llm_config.model
            max_tokens = llm_config.max_tokens
            temperature = llm_config.temperature
            
            logger.info(f"从Nacos获取LLM配置: provider={llm_config.provider}, model={model}")
        except Exception as e:
            logger.warning(f"从Nacos获取LLM配置失败: {e}，降级到环境变量")
            # 降级到环境变量
            api_key = os.getenv("DASHSCOPE_API_KEY")
            base_url = os.getenv("LLM_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
            model = os.getenv("LLM_MODEL", "qwen-plus")
            max_tokens = int(os.getenv("LLM_MAX_TOKENS", "8192"))
            temperature = float(os.getenv("LLM_TEMPERATURE", "0.7"))
    else:
        # 降级到环境变量
        logger.info("配置管理器未初始化，使用环境变量")
        api_key = os.getenv("DASHSCOPE_API_KEY")
        base_url = os.getenv("LLM_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
        model = os.getenv("LLM_MODEL", "qwen-plus")
        max_tokens = int(os.getenv("LLM_MAX_TOKENS", "8192"))
        temperature = float(os.getenv("LLM_TEMPERATURE", "0.7"))

    if not api_key:
        logger.error("LLM API Key未配置，请在Nacos中设置或设置DASHSCOPE_API_KEY环境变量")
        nerfreal.put_msg_txt("抱歉，语言模型配置有误，无法回答您的问题。")
        return

    try:
        # 创建OpenAI客户端
        client = OpenAI(
            api_key=api_key,
            base_url=base_url,
        )
        
        # 构建对话消息
        messages = [
            {
                "role": "system", 
                "content": "你是一个友善、有帮助的AI助手。请用简洁、自然的语言回答用户的问题。"
            },
            {
                "role": "user", 
                "content": message
            }
        ]
        
        logger.info(f"发送LLM请求: model={model}, message_length={len(message)}")
        
        # 调用LLM API
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
            stream=False
        )
        
        # 获取回复内容
        reply = response.choices[0].message.content
        
        # 计算耗时
        elapsed_time = time.perf_counter() - start
        logger.info(f"LLM响应完成，耗时: {elapsed_time:.2f}秒，回复长度: {len(reply)}")
        
        # 发送回复给数字人
        nerfreal.put_msg_txt(reply)
        
    except Exception as e:
        logger.error(f"LLM API调用失败: {e}")
        error_message = "抱歉，我现在无法回答您的问题，请稍后再试。"
        nerfreal.put_msg_txt(error_message)

def llm_response_stream(message: str, nerfreal: BaseReal):
    """
    流式LLM响应函数
    
    Args:
        message: 用户输入的消息
        nerfreal: BaseReal实例
    """
    start = time.perf_counter()
    
    try:
        from openai import OpenAI
    except ImportError:
        logger.error("OpenAI库未安装，请运行: pip install openai")
        nerfreal.put_msg_txt("抱歉，语言模型组件未正确安装。")
        return

    # 获取配置
    config_manager = get_config_manager()
    if config_manager:
        try:
            llm_config = config_manager.get_llm_config()
            api_key = llm_config.api_key
            base_url = llm_config.base_url
            model = llm_config.model
            max_tokens = llm_config.max_tokens
            temperature = llm_config.temperature
        except Exception as e:
            logger.warning(f"从Nacos获取LLM配置失败: {e}，降级到环境变量")
            api_key = os.getenv("DASHSCOPE_API_KEY")
            base_url = os.getenv("LLM_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
            model = os.getenv("LLM_MODEL", "qwen-plus")
            max_tokens = int(os.getenv("LLM_MAX_TOKENS", "8192"))
            temperature = float(os.getenv("LLM_TEMPERATURE", "0.7"))
    else:
        api_key = os.getenv("DASHSCOPE_API_KEY")
        base_url = os.getenv("LLM_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
        model = os.getenv("LLM_MODEL", "qwen-plus")
        max_tokens = int(os.getenv("LLM_MAX_TOKENS", "8192"))
        temperature = float(os.getenv("LLM_TEMPERATURE", "0.7"))

    if not api_key:
        logger.error("LLM API Key未配置")
        nerfreal.put_msg_txt("抱歉，语言模型配置有误，无法回答您的问题。")
        return

    try:
        client = OpenAI(api_key=api_key, base_url=base_url)
        
        messages = [
            {
                "role": "system", 
                "content": "你是一个友善、有帮助的AI助手。请用简洁、自然的语言回答用户的问题。"
            },
            {
                "role": "user", 
                "content": message
            }
        ]
        
        logger.info(f"发送流式LLM请求: model={model}")
        
        # 流式调用
        stream = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
            stream=True
        )
        
        # 收集流式响应
        full_response = ""
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                content = chunk.choices[0].delta.content
                full_response += content
        
        elapsed_time = time.perf_counter() - start
        logger.info(f"流式LLM响应完成，耗时: {elapsed_time:.2f}秒")
        
        # 发送完整回复
        nerfreal.put_msg_txt(full_response)
        
    except Exception as e:
        logger.error(f"流式LLM API调用失败: {e}")
        nerfreal.put_msg_txt("抱歉，我现在无法回答您的问题，请稍后再试。")

def get_llm_client() -> Optional[object]:
    """
    获取配置好的LLM客户端
    
    Returns:
        OpenAI客户端实例或None
    """
    try:
        from openai import OpenAI
    except ImportError:
        logger.error("OpenAI库未安装")
        return None
    
    # 获取配置
    config_manager = get_config_manager()
    if config_manager:
        try:
            llm_config = config_manager.get_llm_config()
            api_key = llm_config.api_key
            base_url = llm_config.base_url
        except Exception as e:
            logger.warning(f"从Nacos获取LLM配置失败: {e}")
            api_key = os.getenv("DASHSCOPE_API_KEY")
            base_url = os.getenv("LLM_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
    else:
        api_key = os.getenv("DASHSCOPE_API_KEY")
        base_url = os.getenv("LLM_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
    
    if not api_key:
        logger.error("LLM API Key未配置")
        return None
    
    try:
        return OpenAI(api_key=api_key, base_url=base_url)
    except Exception as e:
        logger.error(f"创建LLM客户端失败: {e}")
        return None

def test_llm_config():
    """测试LLM配置"""
    print("=== LLM配置测试 ===")
    
    # 测试配置获取
    config_manager = get_config_manager()
    if config_manager:
        try:
            llm_config = config_manager.get_llm_config()
            print(f"✅ Provider: {llm_config.provider}")
            print(f"✅ Model: {llm_config.model}")
            print(f"✅ Base URL: {llm_config.base_url}")
            print(f"✅ API Key: {llm_config.api_key[:20]}..." if llm_config.api_key else "未配置")
        except Exception as e:
            print(f"❌ 获取配置失败: {e}")
    else:
        print("❌ 配置管理器未初始化")
    
    # 测试客户端创建
    client = get_llm_client()
    if client:
        print("✅ LLM客户端创建成功")
    else:
        print("❌ LLM客户端创建失败")

if __name__ == "__main__":
    # 测试配置
    test_llm_config()
