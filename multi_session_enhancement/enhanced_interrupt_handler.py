"""
增强版打断处理器 - 集成到LiveTalking应用
提供Web API和WebSocket接口支持智能打断
"""
import asyncio
import json
import logging
from typing import Dict, Any, Optional
from aiohttp import web, WSMsgType
import aiohttp

from .interrupt_manager import (
    InterruptManager,
    InterruptConfig,
    InterruptEvent,
    InterruptType,
    init_interrupt_manager,
    get_interrupt_manager
)

logger = logging.getLogger(__name__)

class EnhancedInterruptHandler:
    """增强版打断处理器"""
    
    def __init__(self, interrupt_manager: InterruptManager):
        self.interrupt_manager = interrupt_manager
        self.active_websockets: Dict[str, web.WebSocketResponse] = {}
    
    async def handle_interrupt_websocket(self, request):
        """处理打断WebSocket连接"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        session_id = request.query.get('session_id', 'default')
        self.active_websockets[session_id] = ws
        
        logger.info(f"打断WebSocket连接建立: {session_id}")
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    await self._handle_websocket_message(session_id, msg.data)
                elif msg.type == WSMsgType.BINARY:
                    await self._handle_websocket_audio(session_id, msg.data)
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'打断WebSocket错误: {ws.exception()}')
                    break
        except Exception as e:
            logger.error(f"打断WebSocket处理错误: {e}")
        finally:
            if session_id in self.active_websockets:
                del self.active_websockets[session_id]
            logger.info(f"打断WebSocket连接关闭: {session_id}")
        
        return ws
    
    async def _handle_websocket_message(self, session_id: str, message: str):
        """处理WebSocket文本消息"""
        try:
            data = json.loads(message)
            command = data.get('command')
            
            if command == 'check_text':
                # 检查文本是否包含打断指令
                text = data.get('text', '')
                is_interrupt = self.interrupt_manager.process_text_input(session_id, text)
                
                response = {
                    'type': 'interrupt_check',
                    'is_interrupt': is_interrupt,
                    'text': text
                }
                await self.active_websockets[session_id].send_text(json.dumps(response))
                
            elif command == 'manual_interrupt':
                # 手动打断
                self.interrupt_manager.manual_interrupt(session_id)
                
                response = {
                    'type': 'interrupt_executed',
                    'method': 'manual'
                }
                await self.active_websockets[session_id].send_text(json.dumps(response))
                
            elif command == 'config_update':
                # 更新打断配置
                config_data = data.get('config', {})
                await self._update_interrupt_config(session_id, config_data)
                
        except json.JSONDecodeError:
            logger.error(f"无效的WebSocket JSON消息: {message}")
        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {e}")
    
    async def _handle_websocket_audio(self, session_id: str, audio_data: bytes):
        """处理WebSocket音频数据"""
        try:
            # 处理音频数据进行语音活动检测
            self.interrupt_manager.process_audio_input(session_id, audio_data)
        except Exception as e:
            logger.error(f"处理WebSocket音频失败: {e}")
    
    async def _update_interrupt_config(self, session_id: str, config_data: Dict[str, Any]):
        """更新打断配置"""
        try:
            # 这里可以实现会话级别的配置更新
            logger.info(f"更新打断配置: {session_id} - {config_data}")
            
            response = {
                'type': 'config_updated',
                'config': config_data
            }
            await self.active_websockets[session_id].send_text(json.dumps(response))
            
        except Exception as e:
            logger.error(f"更新打断配置失败: {e}")

class InterruptAPIHandler:
    """打断API处理器"""
    
    def __init__(self, interrupt_manager: InterruptManager):
        self.interrupt_manager = interrupt_manager
    
    async def interrupt_manual(self, request):
        """手动打断API"""
        try:
            data = await request.json()
            session_id = data.get('sessionid', 'default')
            
            # 执行手动打断
            self.interrupt_manager.manual_interrupt(session_id)
            
            return web.json_response({
                'code': 0,
                'msg': '打断成功',
                'session_id': session_id
            })
            
        except Exception as e:
            logger.error(f"手动打断失败: {e}")
            return web.json_response({
                'code': -1,
                'msg': str(e)
            }, status=500)
    
    async def check_interrupt_text(self, request):
        """检查文本打断API"""
        try:
            data = await request.json()
            session_id = data.get('sessionid', 'default')
            text = data.get('text', '')
            
            # 检查是否包含打断指令
            is_interrupt = self.interrupt_manager.process_text_input(session_id, text)
            
            return web.json_response({
                'code': 0,
                'is_interrupt': is_interrupt,
                'text': text,
                'session_id': session_id
            })
            
        except Exception as e:
            logger.error(f"检查文本打断失败: {e}")
            return web.json_response({
                'code': -1,
                'msg': str(e)
            }, status=500)
    
    async def get_interrupt_config(self, request):
        """获取打断配置API"""
        try:
            config = self.interrupt_manager.config
            
            return web.json_response({
                'code': 0,
                'config': {
                    'interrupt_keywords': config.interrupt_keywords,
                    'keyword_sensitivity': config.keyword_sensitivity,
                    'voice_activity_threshold': config.voice_activity_threshold,
                    'voice_activity_duration': config.voice_activity_duration,
                    'max_speaking_duration': config.max_speaking_duration,
                    'interrupt_delay': config.interrupt_delay,
                    'smart_interrupt': config.smart_interrupt,
                    'sentence_boundary_detection': config.sentence_boundary_detection
                }
            })
            
        except Exception as e:
            logger.error(f"获取打断配置失败: {e}")
            return web.json_response({
                'code': -1,
                'msg': str(e)
            }, status=500)
    
    async def update_interrupt_config(self, request):
        """更新打断配置API"""
        try:
            data = await request.json()
            config_updates = data.get('config', {})
            
            # 更新配置
            config = self.interrupt_manager.config
            for key, value in config_updates.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            
            return web.json_response({
                'code': 0,
                'msg': '配置更新成功',
                'config': config_updates
            })
            
        except Exception as e:
            logger.error(f"更新打断配置失败: {e}")
            return web.json_response({
                'code': -1,
                'msg': str(e)
            }, status=500)
    
    async def get_session_status(self, request):
        """获取会话状态API"""
        try:
            session_id = request.query.get('session_id', 'default')
            
            if session_id in self.interrupt_manager.active_sessions:
                session = self.interrupt_manager.active_sessions[session_id]
                nerfreal = session['nerfreal']
                
                status = {
                    'session_id': session_id,
                    'is_speaking': nerfreal.is_speaking(),
                    'speaking_start_time': session.get('speaking_start_time'),
                    'interrupt_count': session.get('interrupt_count', 0),
                    'last_interrupt_time': session.get('last_interrupt_time')
                }
            else:
                status = {
                    'session_id': session_id,
                    'exists': False
                }
            
            return web.json_response({
                'code': 0,
                'status': status
            })
            
        except Exception as e:
            logger.error(f"获取会话状态失败: {e}")
            return web.json_response({
                'code': -1,
                'msg': str(e)
            }, status=500)

def setup_interrupt_routes(app: web.Application, interrupt_manager: InterruptManager):
    """设置打断相关路由"""
    
    # WebSocket处理器
    interrupt_handler = EnhancedInterruptHandler(interrupt_manager)
    
    # API处理器
    api_handler = InterruptAPIHandler(interrupt_manager)
    
    # 添加路由
    app.router.add_get('/interrupt/ws', interrupt_handler.handle_interrupt_websocket)
    app.router.add_post('/interrupt/manual', api_handler.interrupt_manual)
    app.router.add_post('/interrupt/check_text', api_handler.check_interrupt_text)
    app.router.add_get('/interrupt/config', api_handler.get_interrupt_config)
    app.router.add_post('/interrupt/config', api_handler.update_interrupt_config)
    app.router.add_get('/interrupt/status', api_handler.get_session_status)
    
    # 兼容原有的打断接口
    app.router.add_post('/interrupt_talk', api_handler.interrupt_manual)
    
    logger.info("打断路由设置完成")

async def init_enhanced_interrupt_system(app: web.Application, config: InterruptConfig = None):
    """初始化增强版打断系统"""
    try:
        # 初始化打断管理器
        interrupt_manager = init_interrupt_manager(config)
        
        # 设置路由
        setup_interrupt_routes(app, interrupt_manager)
        
        logger.info("增强版打断系统初始化完成")
        return interrupt_manager
        
    except Exception as e:
        logger.error(f"初始化增强版打断系统失败: {e}")
        raise

def integrate_with_nerfreal(nerfreal_instance, session_id: str):
    """集成到nerfreal实例"""
    interrupt_manager = get_interrupt_manager()
    if interrupt_manager:
        # 注册会话
        interrupt_manager.register_session(session_id, nerfreal_instance)
        
        # 添加打断回调
        def on_interrupt(event):
            logger.info(f"会话 {session_id} 被打断: {event.interrupt_type.value}")
        
        interrupt_manager.add_interrupt_callback(session_id, on_interrupt)
        
        # 监听说话状态变化
        original_put_msg_txt = nerfreal_instance.put_msg_txt
        
        def enhanced_put_msg_txt(msg, eventpoint=None):
            # 数字人开始说话
            interrupt_manager.on_speaking_start(session_id)
            return original_put_msg_txt(msg, eventpoint)
        
        nerfreal_instance.put_msg_txt = enhanced_put_msg_txt
        
        logger.info(f"nerfreal实例已集成打断功能: {session_id}")

# 修改原有的human处理函数以支持智能打断
async def enhanced_human_handler(request):
    """增强版human处理函数 - 支持智能打断"""
    try:
        params = await request.json()
        session_id = params.get('sessionid', 0)
        text = params.get('text', '')
        msg_type = params.get('type', 'chat')
        
        # 获取打断管理器
        interrupt_manager = get_interrupt_manager()
        
        # 检查是否为打断指令
        if interrupt_manager and text:
            is_interrupt = interrupt_manager.process_text_input(str(session_id), text)
            if is_interrupt:
                return web.json_response({
                    'code': 0,
                    'msg': '检测到打断指令，已执行打断',
                    'interrupted': True
                })
        
        # 原有的处理逻辑
        # 这里需要根据实际的app.py中的逻辑进行调整
        
        return web.json_response({
            'code': 0,
            'msg': 'ok',
            'interrupted': False
        })
        
    except Exception as e:
        logger.error(f"增强版human处理失败: {e}")
        return web.json_response({
            'code': -1,
            'msg': str(e)
        }, status=500)
