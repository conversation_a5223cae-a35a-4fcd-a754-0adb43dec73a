"""
增强的CosyVoice TTS实现 - 优化GPUStack部署性能
"""
import os
import time
import logging
import asyncio
from typing import Iterator, Optional
import requests
import aiohttp
from ttsreal import BaseTTS, State
from cosyvoice_voice_manager import get_voice_manager, CosyVoiceManager

logger = logging.getLogger(__name__)

class EnhancedCosyVoiceTTS(BaseTTS):
    """增强版CosyVoice TTS - 支持音色预注册和缓存"""
    
    def __init__(self, opt, parent):
        super().__init__(opt, parent)
        self.voice_manager = get_voice_manager(opt.TTS_SERVER)
        self.current_voice_id = None
        self._setup_voice()
    
    def _setup_voice(self):
        """设置当前使用的音色"""
        try:
            # 检查REF_FILE是否是已注册的音色ID
            voice_info = self.voice_manager.get_voice_info(self.opt.REF_FILE)
            if voice_info:
                self.current_voice_id = self.opt.REF_FILE
                logger.info(f"使用已注册音色: {self.current_voice_id}")
            else:
                # 如果是文件路径，尝试自动注册
                if os.path.exists(self.opt.REF_FILE):
                    self._auto_register_voice()
                else:
                    logger.warning(f"音色文件不存在且未注册: {self.opt.REF_FILE}")
        except Exception as e:
            logger.error(f"设置音色失败: {e}")
    
    def _auto_register_voice(self):
        """自动注册音色"""
        try:
            # 生成音色ID
            import hashlib
            voice_id = hashlib.md5(self.opt.REF_FILE.encode()).hexdigest()[:8]
            
            # 异步注册音色
            asyncio.create_task(self._register_voice_async(
                voice_id=voice_id,
                name=f"Auto-{os.path.basename(self.opt.REF_FILE)}",
                audio_path=self.opt.REF_FILE,
                ref_text=self.opt.REF_TEXT or "这是自动注册的音色",
                description="自动注册的音色"
            ))
            
            self.current_voice_id = voice_id
            logger.info(f"自动注册音色: {voice_id}")
            
        except Exception as e:
            logger.error(f"自动注册音色失败: {e}")
    
    async def _register_voice_async(self, voice_id: str, name: str, audio_path: str, ref_text: str, description: str):
        """异步注册音色"""
        try:
            async with CosyVoiceManager(self.opt.TTS_SERVER) as manager:
                success = await manager.register_voice(voice_id, name, audio_path, ref_text, description)
                if success:
                    logger.info(f"音色 {voice_id} 注册成功")
                else:
                    logger.error(f"音色 {voice_id} 注册失败")
        except Exception as e:
            logger.error(f"异步注册音色失败: {e}")
    
    def txt_to_audio(self, msg):
        """文本转语音 - 优化版本"""
        text, textevent = msg
        
        # 优先使用音色管理器
        if self.current_voice_id:
            self.stream_tts(
                self._synthesize_with_voice_manager(text),
                msg
            )
        else:
            # 回退到原始实现
            self.stream_tts(
                self._synthesize_with_file_upload(text),
                msg
            )
    
    def _synthesize_with_voice_manager(self, text: str) -> Iterator[bytes]:
        """使用音色管理器合成语音"""
        try:
            start = time.perf_counter()
            
            # 使用异步方法合成
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                audio_data = loop.run_until_complete(self._async_synthesize(text))
                if audio_data:
                    end = time.perf_counter()
                    logger.info(f"CosyVoice合成时间: {end-start:.4f}s")
                    
                    # 分块返回音频数据
                    chunk_size = 9600  # 24K*20ms*2
                    for i in range(0, len(audio_data), chunk_size):
                        if self.state == State.RUNNING:
                            yield audio_data[i:i+chunk_size]
                        else:
                            break
                else:
                    logger.error("音色管理器合成失败，回退到文件上传")
                    yield from self._synthesize_with_file_upload(text)
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"音色管理器合成失败: {e}")
            # 回退到原始方法
            yield from self._synthesize_with_file_upload(text)
    
    async def _async_synthesize(self, text: str) -> Optional[bytes]:
        """异步合成语音"""
        try:
            async with CosyVoiceManager(self.opt.TTS_SERVER) as manager:
                return await manager.synthesize_with_voice(text, self.current_voice_id)
        except Exception as e:
            logger.error(f"异步合成失败: {e}")
            return None
    
    def _synthesize_with_file_upload(self, text: str) -> Iterator[bytes]:
        """文件上传方式合成 (原始方法)"""
        try:
            start = time.perf_counter()
            payload = {
                'tts_text': text,
                'prompt_text': self.opt.REF_TEXT
            }
            
            files = [('prompt_wav', ('prompt_wav', open(self.opt.REF_FILE, 'rb'), 'application/octet-stream'))]
            res = requests.request("GET", f"{self.opt.TTS_SERVER}/inference_zero_shot", 
                                 data=payload, files=files, stream=True)
            
            end = time.perf_counter()
            logger.info(f"CosyVoice文件上传合成时间: {end-start:.4f}s")
            
            if res.status_code != 200:
                logger.error("CosyVoice错误: %s", res.text)
                return
            
            first = True
            for chunk in res.iter_content(chunk_size=9600):
                if first:
                    end = time.perf_counter()
                    logger.info(f"CosyVoice首个音频块时间: {end-start:.4f}s")
                    first = False
                if chunk and self.state == State.RUNNING:
                    yield chunk
                    
        except Exception as e:
            logger.exception('CosyVoice合成失败')
    
    def update_voice(self, voice_id_or_path: str, ref_text: str = None):
        """更新当前使用的音色"""
        try:
            # 检查是否是已注册的音色ID
            voice_info = self.voice_manager.get_voice_info(voice_id_or_path)
            if voice_info:
                self.current_voice_id = voice_id_or_path
                self.opt.REF_FILE = voice_info['audio_path']
                self.opt.REF_TEXT = ref_text or voice_info['ref_text']
                logger.info(f"切换到音色: {voice_id_or_path}")
            else:
                # 如果是文件路径，更新配置
                if os.path.exists(voice_id_or_path):
                    self.opt.REF_FILE = voice_id_or_path
                    if ref_text:
                        self.opt.REF_TEXT = ref_text
                    self.current_voice_id = None
                    self._auto_register_voice()
                    logger.info(f"更新音色文件: {voice_id_or_path}")
                else:
                    logger.error(f"音色不存在: {voice_id_or_path}")
                    
        except Exception as e:
            logger.error(f"更新音色失败: {e}")
    
    def get_voice_list(self) -> list:
        """获取可用音色列表"""
        try:
            return self.voice_manager.list_voices()
        except Exception as e:
            logger.error(f"获取音色列表失败: {e}")
            return []

class CosyVoiceVoiceAPI:
    """CosyVoice音色管理API"""
    
    def __init__(self, voice_manager: CosyVoiceManager):
        self.voice_manager = voice_manager
    
    async def register_voice(self, request):
        """注册新音色API"""
        try:
            # 处理multipart/form-data
            reader = await request.multipart()
            
            voice_id = None
            name = None
            ref_text = None
            description = ""
            audio_file = None
            
            async for field in reader:
                if field.name == 'voice_id':
                    voice_id = await field.text()
                elif field.name == 'name':
                    name = await field.text()
                elif field.name == 'ref_text':
                    ref_text = await field.text()
                elif field.name == 'description':
                    description = await field.text()
                elif field.name == 'audio_file':
                    # 保存音频文件
                    filename = field.filename or f"{voice_id}.wav"
                    audio_path = f"data/voices/{filename}"
                    
                    os.makedirs(os.path.dirname(audio_path), exist_ok=True)
                    
                    with open(audio_path, 'wb') as f:
                        async for chunk in field:
                            f.write(chunk)
                    
                    audio_file = audio_path
            
            if not all([voice_id, name, ref_text, audio_file]):
                return {"error": "缺少必需参数", "code": -1}
            
            # 注册音色
            success = await self.voice_manager.register_voice(
                voice_id, name, audio_file, ref_text, description
            )
            
            if success:
                return {"message": "音色注册成功", "voice_id": voice_id, "code": 0}
            else:
                return {"error": "音色注册失败", "code": -1}
                
        except Exception as e:
            logger.error(f"注册音色API失败: {e}")
            return {"error": str(e), "code": -1}
    
    async def list_voices(self, request):
        """列出音色API"""
        try:
            voices = self.voice_manager.list_voices()
            return {"voices": voices, "total": len(voices), "code": 0}
        except Exception as e:
            logger.error(f"列出音色API失败: {e}")
            return {"error": str(e), "code": -1}
    
    async def get_voice_info(self, request):
        """获取音色信息API"""
        try:
            voice_id = request.match_info.get('voice_id')
            if not voice_id:
                return {"error": "缺少voice_id参数", "code": -1}
            
            voice_info = self.voice_manager.get_voice_info(voice_id)
            if voice_info:
                return {"voice_info": voice_info, "code": 0}
            else:
                return {"error": "音色不存在", "code": -1}
                
        except Exception as e:
            logger.error(f"获取音色信息API失败: {e}")
            return {"error": str(e), "code": -1}
    
    async def remove_voice(self, request):
        """删除音色API"""
        try:
            voice_id = request.match_info.get('voice_id')
            if not voice_id:
                return {"error": "缺少voice_id参数", "code": -1}
            
            success = self.voice_manager.remove_voice(voice_id)
            if success:
                return {"message": "音色删除成功", "code": 0}
            else:
                return {"error": "音色不存在", "code": -1}
                
        except Exception as e:
            logger.error(f"删除音色API失败: {e}")
            return {"error": str(e), "code": -1}

def setup_cosyvoice_routes(app, voice_manager: CosyVoiceManager):
    """设置CosyVoice音色管理路由"""
    from aiohttp import web
    
    api = CosyVoiceVoiceAPI(voice_manager)
    
    # 音色管理路由
    app.router.add_post('/api/cosyvoice/voices', api.register_voice)
    app.router.add_get('/api/cosyvoice/voices', api.list_voices)
    app.router.add_get('/api/cosyvoice/voices/{voice_id}', api.get_voice_info)
    app.router.add_delete('/api/cosyvoice/voices/{voice_id}', api.remove_voice)
    
    logger.info("CosyVoice音色管理API路由设置完成")
