"""
资源管理器 - 智能加载和缓存avatar、模型等资源
"""
import os
import threading
import weakref
from typing import Dict, Optional, Any, Tuple
import logging
import gc
import torch

logger = logging.getLogger(__name__)

class ResourceCache:
    """资源缓存类"""
    
    def __init__(self, max_size: int = 10):
        self.cache: Dict[str, Any] = {}
        self.ref_counts: Dict[str, int] = {}
        self.access_order: list = []  # LRU顺序
        self.max_size = max_size
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存资源"""
        with self.lock:
            if key in self.cache:
                # 更新访问顺序
                if key in self.access_order:
                    self.access_order.remove(key)
                self.access_order.append(key)
                return self.cache[key]
            return None
    
    def put(self, key: str, value: Any):
        """放入缓存"""
        with self.lock:
            # 如果缓存已满，移除最少使用的项
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            self.cache[key] = value
            self.ref_counts[key] = 0
            
            if key in self.access_order:
                self.access_order.remove(key)
            self.access_order.append(key)
    
    def add_ref(self, key: str):
        """增加引用计数"""
        with self.lock:
            if key in self.ref_counts:
                self.ref_counts[key] += 1
    
    def remove_ref(self, key: str):
        """减少引用计数"""
        with self.lock:
            if key in self.ref_counts:
                self.ref_counts[key] = max(0, self.ref_counts[key] - 1)
    
    def _evict_lru(self):
        """移除最少使用的项"""
        if not self.access_order:
            return
        
        # 找到引用计数为0的最旧项
        for key in self.access_order:
            if self.ref_counts.get(key, 0) == 0:
                self._remove_item(key)
                break
        else:
            # 如果所有项都有引用，移除最旧的
            key = self.access_order[0]
            self._remove_item(key)
    
    def _remove_item(self, key: str):
        """移除缓存项"""
        if key in self.cache:
            del self.cache[key]
        if key in self.ref_counts:
            del self.ref_counts[key]
        if key in self.access_order:
            self.access_order.remove(key)
        logger.info(f"Evicted resource from cache: {key}")
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.ref_counts.clear()
            self.access_order.clear()
            gc.collect()

class ResourceManager:
    """资源管理器"""
    
    def __init__(self, cache_size: int = 10):
        self.model_cache = ResourceCache(cache_size)
        self.avatar_cache = ResourceCache(cache_size)
        self.lock = threading.RLock()
        self._loaded_models = set()
        self._loaded_avatars = set()
    
    def load_model(self, model_type: str, model_path: Optional[str] = None) -> Any:
        """加载模型"""
        cache_key = f"{model_type}_{model_path or 'default'}"
        
        # 尝试从缓存获取
        cached_model = self.model_cache.get(cache_key)
        if cached_model is not None:
            self.model_cache.add_ref(cache_key)
            logger.info(f"Loaded model from cache: {cache_key}")
            return cached_model
        
        # 加载新模型
        model = self._load_model_impl(model_type, model_path)
        if model is not None:
            self.model_cache.put(cache_key, model)
            self.model_cache.add_ref(cache_key)
            self._loaded_models.add(cache_key)
            logger.info(f"Loaded new model: {cache_key}")
        
        return model
    
    def _load_model_impl(self, model_type: str, model_path: Optional[str] = None) -> Any:
        """实际加载模型的实现"""
        try:
            if model_type == 'musetalk':
                from musereal import load_model
                return load_model()
            elif model_type == 'wav2lip':
                from lipreal import load_model
                path = model_path or "./models/wav2lip.pth"
                return load_model(path)
            elif model_type == 'ultralight':
                from lightreal import load_model
                # 需要传入opt参数，这里简化处理
                return None  # 实际实现需要根据具体需求调整
            else:
                logger.error(f"Unknown model type: {model_type}")
                return None
        except Exception as e:
            logger.error(f"Failed to load model {model_type}: {e}")
            return None
    
    def load_avatar(self, avatar_id: str) -> Any:
        """加载avatar"""
        cache_key = f"avatar_{avatar_id}"
        
        # 尝试从缓存获取
        cached_avatar = self.avatar_cache.get(cache_key)
        if cached_avatar is not None:
            self.avatar_cache.add_ref(cache_key)
            logger.info(f"Loaded avatar from cache: {cache_key}")
            return cached_avatar
        
        # 加载新avatar
        avatar = self._load_avatar_impl(avatar_id)
        if avatar is not None:
            self.avatar_cache.put(cache_key, avatar)
            self.avatar_cache.add_ref(cache_key)
            self._loaded_avatars.add(cache_key)
            logger.info(f"Loaded new avatar: {cache_key}")
        
        return avatar
    
    def _load_avatar_impl(self, avatar_id: str) -> Any:
        """实际加载avatar的实现"""
        try:
            avatar_path = f"./data/avatars/{avatar_id}"
            if not os.path.exists(avatar_path):
                logger.error(f"Avatar path not found: {avatar_path}")
                return None
            
            # 这里需要根据具体的avatar类型来加载
            # 简化实现，实际需要根据模型类型选择对应的load_avatar函数
            from musereal import load_avatar
            return load_avatar(avatar_id)
        except Exception as e:
            logger.error(f"Failed to load avatar {avatar_id}: {e}")
            return None
    
    def release_model(self, model_type: str, model_path: Optional[str] = None):
        """释放模型引用"""
        cache_key = f"{model_type}_{model_path or 'default'}"
        self.model_cache.remove_ref(cache_key)
    
    def release_avatar(self, avatar_id: str):
        """释放avatar引用"""
        cache_key = f"avatar_{avatar_id}"
        self.avatar_cache.remove_ref(cache_key)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            return {
                'models': {
                    'cached': len(self.model_cache.cache),
                    'loaded': len(self._loaded_models),
                    'ref_counts': dict(self.model_cache.ref_counts)
                },
                'avatars': {
                    'cached': len(self.avatar_cache.cache),
                    'loaded': len(self._loaded_avatars),
                    'ref_counts': dict(self.avatar_cache.ref_counts)
                }
            }
    
    def clear_cache(self):
        """清空所有缓存"""
        with self.lock:
            self.model_cache.clear()
            self.avatar_cache.clear()
            self._loaded_models.clear()
            self._loaded_avatars.clear()
            logger.info("Cleared all resource caches")
    
    def preload_resources(self, configs: list):
        """预加载资源"""
        for config in configs:
            model_type = config.get('model')
            avatar_id = config.get('avatar_id')
            
            if model_type:
                self.load_model(model_type)
            if avatar_id:
                self.load_avatar(avatar_id)
        
        logger.info(f"Preloaded {len(configs)} resource configurations")

# 全局资源管理器实例
resource_manager = ResourceManager()
