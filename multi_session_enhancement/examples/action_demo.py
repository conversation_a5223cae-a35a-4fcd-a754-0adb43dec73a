"""
自定义动作功能演示
展示如何创建、管理和使用数字人动作
"""
import asyncio
import json
import logging
import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from custom_action_manager import (
    CustomActionManager, 
    ActionConfig, 
    ActionType, 
    ActionLibrary,
    init_action_manager,
    get_action_manager
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ActionDemo:
    """动作功能演示"""
    
    def __init__(self):
        self.action_manager = None
    
    def setup_action_system(self):
        """设置动作系统"""
        logger.info("初始化动作管理系统...")
        
        # 初始化动作管理器
        self.action_manager = init_action_manager(
            config_path="demo_custom_config.json",
            actions_dir="demo_customvideo"
        )
        
        logger.info("动作管理系统初始化完成")
    
    def demo_create_actions(self):
        """演示创建动作"""
        print("\n=== 创建自定义动作演示 ===")
        
        # 创建各种类型的动作
        demo_actions = [
            ActionConfig(
                audiotype=10,
                action_name="热情欢迎",
                action_type=ActionType.GREETING,
                imgpath="demo_customvideo/welcome/images",
                audiopath="demo_customvideo/welcome/audio.wav",
                description="热情的欢迎手势，双手张开",
                trigger_keywords=["欢迎", "welcome", "你好", "hello"],
                emotion_score=0.9,
                gesture_intensity=0.8
            ),
            ActionConfig(
                audiotype=11,
                action_name="专业解释",
                action_type=ActionType.EXPLAINING,
                imgpath="demo_customvideo/explaining/images",
                audiopath="demo_customvideo/explaining/audio.wav",
                description="专业的解释手势，手指指向",
                trigger_keywords=["解释", "说明", "介绍", "explain"],
                emotion_score=0.3,
                gesture_intensity=0.6
            ),
            ActionConfig(
                audiotype=12,
                action_name="惊喜表情",
                action_type=ActionType.SURPRISED,
                imgpath="demo_customvideo/surprised/images",
                audiopath="demo_customvideo/surprised/audio.wav",
                description="表示惊喜的表情和手势",
                trigger_keywords=["哇", "惊喜", "amazing", "wow"],
                emotion_score=0.7,
                gesture_intensity=0.9
            ),
            ActionConfig(
                audiotype=13,
                action_name="深度思考",
                action_type=ActionType.THINKING,
                imgpath="demo_customvideo/deep_thinking/images",
                audiopath="demo_customvideo/deep_thinking/audio.wav",
                description="深度思考时的手势，手托下巴",
                trigger_keywords=["思考", "考虑", "想想", "think"],
                emotion_score=0.0,
                gesture_intensity=0.4
            ),
            ActionConfig(
                audiotype=14,
                action_name="展示产品",
                action_type=ActionType.PRESENTING,
                imgpath="demo_customvideo/presenting/images",
                audiopath="demo_customvideo/presenting/audio.wav",
                description="展示产品时的专业手势",
                trigger_keywords=["展示", "介绍", "看这里", "present"],
                emotion_score=0.5,
                gesture_intensity=0.7
            )
        ]
        
        # 添加动作到管理器
        for action in demo_actions:
            success = self.action_manager.add_action(action)
            if success:
                print(f"✅ 创建动作成功: {action.action_name}")
            else:
                print(f"❌ 创建动作失败: {action.action_name}")
    
    def demo_search_actions(self):
        """演示搜索动作"""
        print("\n=== 动作搜索演示 ===")
        
        # 按关键词搜索
        print("🔍 按关键词搜索:")
        test_keywords = ["欢迎", "思考", "解释", "惊喜"]
        
        for keyword in test_keywords:
            actions = self.action_manager.find_actions_by_keyword(keyword)
            print(f"  关键词 '{keyword}': 找到 {len(actions)} 个动作")
            for action in actions:
                print(f"    - {action.action_name} (类型: {action.action_type.value})")
        
        # 按动作类型搜索
        print("\n🎭 按动作类型搜索:")
        action_types = [ActionType.GREETING, ActionType.THINKING, ActionType.EXPLAINING]
        
        for action_type in action_types:
            actions = self.action_manager.find_actions_by_type(action_type)
            print(f"  类型 '{action_type.value}': 找到 {len(actions)} 个动作")
            for action in actions:
                print(f"    - {action.action_name}")
        
        # 按情感分数搜索
        print("\n😊 按情感分数搜索:")
        emotion_ranges = [
            (0.7, 1.0, "积极情感"),
            (0.0, 0.3, "中性情感"),
            (-1.0, 0.0, "消极情感")
        ]
        
        for min_score, max_score, label in emotion_ranges:
            actions = self.action_manager.find_actions_by_emotion(min_score, max_score)
            print(f"  {label} ({min_score}-{max_score}): 找到 {len(actions)} 个动作")
            for action in actions:
                print(f"    - {action.action_name} (分数: {action.emotion_score})")
    
    def demo_action_management(self):
        """演示动作管理"""
        print("\n=== 动作管理演示 ===")
        
        # 列出所有动作
        all_actions = self.action_manager.list_actions()
        print(f"📋 当前共有 {len(all_actions)} 个动作:")
        
        for audiotype, action in all_actions.items():
            print(f"  {audiotype}: {action.action_name} ({action.action_type.value})")
            print(f"      描述: {action.description}")
            print(f"      触发词: {', '.join(action.trigger_keywords)}")
            print(f"      情感分数: {action.emotion_score}")
            print()
        
        # 获取特定动作
        print("🎯 获取特定动作:")
        test_audiotype = 10
        action = self.action_manager.get_action(test_audiotype)
        if action:
            print(f"  动作 {test_audiotype}: {action.action_name}")
            print(f"  类型: {action.action_type.value}")
            print(f"  描述: {action.description}")
        else:
            print(f"  动作 {test_audiotype} 不存在")
        
        # 验证动作完整性
        print("\n🔍 验证动作完整性:")
        validation_results = self.action_manager.validate_all_actions()
        
        if validation_results:
            print("  发现以下问题:")
            for audiotype, issues in validation_results.items():
                action_name = all_actions[audiotype].action_name
                print(f"    动作 {audiotype} ({action_name}):")
                for issue in issues:
                    print(f"      - {issue}")
        else:
            print("  ✅ 所有动作验证通过")
    
    def demo_keyword_matching(self):
        """演示关键词匹配"""
        print("\n=== 关键词匹配演示 ===")
        
        test_phrases = [
            "你好，欢迎来到我们的直播间",
            "让我来解释一下这个功能",
            "哇，这真是太惊喜了",
            "我需要思考一下这个问题",
            "现在我来展示一下这个产品",
            "大家好，很高兴见到你们",
            "这个设计真的很棒"
        ]
        
        print("🎯 测试语句的动作匹配:")
        
        for phrase in test_phrases:
            print(f"\n  测试语句: '{phrase}'")
            
            # 分词并查找匹配的动作
            words = phrase.split()
            matched_actions = []
            
            for word in words:
                actions = self.action_manager.find_actions_by_keyword(word)
                for action in actions:
                    if action not in matched_actions:
                        matched_actions.append(action)
            
            if matched_actions:
                print("    匹配的动作:")
                for action in matched_actions:
                    print(f"      - {action.action_name} (类型: {action.action_type.value})")
                    print(f"        触发词: {', '.join(action.trigger_keywords)}")
            else:
                print("    ❌ 未找到匹配的动作")
    
    def demo_export_formats(self):
        """演示导出格式"""
        print("\n=== 导出格式演示 ===")
        
        # 导出为旧格式 (兼容原系统)
        print("📤 导出为旧格式 (兼容原LiveTalking):")
        legacy_format = self.action_manager.export_legacy_format()
        
        print("  旧格式配置:")
        for item in legacy_format:
            print(f"    {json.dumps(item, ensure_ascii=False, indent=2)}")
        
        # 保存为旧格式文件
        legacy_path = "demo_legacy_config.json"
        with open(legacy_path, 'w', encoding='utf-8') as f:
            json.dump(legacy_format, f, ensure_ascii=False, indent=2)
        print(f"  ✅ 已保存到: {legacy_path}")
        
        # 显示新格式
        print("\n📋 新格式配置预览:")
        all_actions = self.action_manager.list_actions()
        sample_action = next(iter(all_actions.values()))
        
        print("  新格式特性:")
        print(f"    - 动作名称: {sample_action.action_name}")
        print(f"    - 动作类型: {sample_action.action_type.value}")
        print(f"    - 触发关键词: {sample_action.trigger_keywords}")
        print(f"    - 情感分数: {sample_action.emotion_score}")
        print(f"    - 手势强度: {sample_action.gesture_intensity}")
        print(f"    - 循环播放: {sample_action.loop}")
        print(f"    - 启用状态: {sample_action.enabled}")
    
    def demo_smart_action_selection(self):
        """演示智能动作选择"""
        print("\n=== 智能动作选择演示 ===")
        
        scenarios = [
            {
                "context": "用户刚进入直播间",
                "emotion": "positive",
                "keywords": ["欢迎", "你好"],
                "expected_type": ActionType.GREETING
            },
            {
                "context": "用户询问复杂问题",
                "emotion": "neutral",
                "keywords": ["解释", "说明"],
                "expected_type": ActionType.EXPLAINING
            },
            {
                "context": "用户表示赞赏",
                "emotion": "positive",
                "keywords": ["棒", "好"],
                "expected_type": ActionType.THUMBS_UP
            },
            {
                "context": "需要思考回答",
                "emotion": "neutral",
                "keywords": ["思考", "想想"],
                "expected_type": ActionType.THINKING
            }
        ]
        
        print("🤖 智能动作选择测试:")
        
        for scenario in scenarios:
            print(f"\n  场景: {scenario['context']}")
            print(f"  情感: {scenario['emotion']}")
            print(f"  关键词: {', '.join(scenario['keywords'])}")
            
            # 查找匹配的动作
            matched_actions = []
            for keyword in scenario['keywords']:
                actions = self.action_manager.find_actions_by_keyword(keyword)
                matched_actions.extend(actions)
            
            # 去重并按相关性排序
            unique_actions = []
            for action in matched_actions:
                if action not in unique_actions:
                    unique_actions.append(action)
            
            # 按情感分数和动作类型排序
            if scenario['emotion'] == 'positive':
                unique_actions.sort(key=lambda x: x.emotion_score, reverse=True)
            elif scenario['emotion'] == 'negative':
                unique_actions.sort(key=lambda x: x.emotion_score)
            
            if unique_actions:
                best_action = unique_actions[0]
                print(f"  推荐动作: {best_action.action_name}")
                print(f"  动作类型: {best_action.action_type.value}")
                print(f"  情感分数: {best_action.emotion_score}")
                print(f"  匹配度: {'高' if best_action.action_type == scenario['expected_type'] else '中'}")
            else:
                print("  ❌ 未找到合适的动作")
    
    def run_all_demos(self):
        """运行所有演示"""
        print("🎭 自定义动作功能演示开始")
        print("=" * 50)
        
        # 设置动作系统
        self.setup_action_system()
        
        # 运行各种演示
        self.demo_create_actions()
        self.demo_search_actions()
        self.demo_action_management()
        self.demo_keyword_matching()
        self.demo_export_formats()
        self.demo_smart_action_selection()
        
        print("\n" + "=" * 50)
        print("🎉 所有演示完成!")
        
        # 显示总结
        all_actions = self.action_manager.list_actions()
        print(f"\n📊 演示总结:")
        print(f"  - 创建动作数量: {len(all_actions)}")
        print(f"  - 支持动作类型: {len(set(action.action_type for action in all_actions.values()))}")
        print(f"  - 总触发关键词: {sum(len(action.trigger_keywords) for action in all_actions.values())}")

def interactive_demo():
    """交互式演示"""
    print("\n🎮 交互式动作管理演示")
    print("输入以下命令:")
    print("  'list' - 列出所有动作")
    print("  'search <关键词>' - 搜索动作")
    print("  'info <audiotype>' - 查看动作详情")
    print("  'create' - 创建新动作")
    print("  'export' - 导出配置")
    print("  'quit' - 退出")
    print()
    
    demo = ActionDemo()
    demo.setup_action_system()
    
    while True:
        user_input = input("👤 请输入命令: ").strip()
        
        if user_input.lower() == 'quit':
            break
        elif user_input.lower() == 'list':
            actions = demo.action_manager.list_actions()
            print(f"📋 共有 {len(actions)} 个动作:")
            for audiotype, action in actions.items():
                print(f"  {audiotype}: {action.action_name} ({action.action_type.value})")
        elif user_input.lower().startswith('search '):
            keyword = user_input[7:]
            actions = demo.action_manager.find_actions_by_keyword(keyword)
            print(f"🔍 关键词 '{keyword}' 搜索结果:")
            for action in actions:
                print(f"  - {action.action_name} (类型: {action.action_type.value})")
        elif user_input.lower().startswith('info '):
            try:
                audiotype = int(user_input[5:])
                action = demo.action_manager.get_action(audiotype)
                if action:
                    print(f"📋 动作 {audiotype} 详情:")
                    print(f"  名称: {action.action_name}")
                    print(f"  类型: {action.action_type.value}")
                    print(f"  描述: {action.description}")
                    print(f"  触发词: {', '.join(action.trigger_keywords)}")
                    print(f"  情感分数: {action.emotion_score}")
                else:
                    print(f"❌ 动作 {audiotype} 不存在")
            except ValueError:
                print("❌ 请输入有效的动作ID")
        elif user_input.lower() == 'export':
            legacy_format = demo.action_manager.export_legacy_format()
            print("📤 旧格式配置:")
            print(json.dumps(legacy_format, ensure_ascii=False, indent=2))
        else:
            print("❌ 未知命令")
    
    print("👋 演示结束")

def main():
    """主函数"""
    print("选择演示模式:")
    print("1. 自动演示所有功能")
    print("2. 交互式演示")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        demo = ActionDemo()
        demo.run_all_demos()
    elif choice == "2":
        interactive_demo()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
