#!/usr/bin/env python3
"""
Nacos配置管理演示
演示如何使用Nacos配置管理器替代环境变量
"""
import sys
import os
import json
import asyncio
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from multi_session_enhancement.nacos_config_manager import (
    NacosConfigManager, APIConfig, LLMConfig, TTSConfig,
    init_config_manager, get_config_manager, get_api_config, get_llm_config, get_tts_config
)
from nacos_service import init_nacos_client

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def demo_basic_usage():
    """演示基本用法"""
    print("=== Nacos配置管理基本用法演示 ===\n")
    
    # 1. 初始化Nacos客户端
    print("1. 初始化Nacos客户端...")
    nacos_client = init_nacos_client(
        server_addresses="127.0.0.1:8848",
        namespace="",
        username="nacos",
        password="nacos"
    )
    
    if not nacos_client:
        print("❌ Nacos客户端初始化失败，请检查Nacos服务是否启动")
        return
    
    print("✅ Nacos客户端初始化成功")
    
    # 2. 初始化配置管理器
    print("\n2. 初始化配置管理器...")
    config_manager = init_config_manager(nacos_client)
    print("✅ 配置管理器初始化成功")
    
    # 3. 获取API配置
    print("\n3. 获取API配置...")
    api_config = get_api_config()
    print(f"DashScope API Key: {api_config.dashscope_api_key[:10]}..." if api_config.dashscope_api_key else "未配置")
    print(f"腾讯云 App ID: {api_config.tencent_appid}")
    print(f"豆包 App ID: {api_config.doubao_appid}")
    
    # 4. 获取LLM配置
    print("\n4. 获取LLM配置...")
    llm_config = get_llm_config()
    print(f"LLM Provider: {llm_config.provider}")
    print(f"LLM Model: {llm_config.model}")
    print(f"Base URL: {llm_config.base_url}")
    
    # 5. 获取TTS配置
    print("\n5. 获取TTS配置...")
    for provider in ["edgetts", "tencent", "doubao"]:
        tts_config = get_tts_config(provider)
        print(f"{provider.upper()} - Voice: {tts_config.voice}")

def demo_config_publishing():
    """演示配置发布"""
    print("\n=== 配置发布演示 ===\n")
    
    config_manager = get_config_manager()
    if not config_manager:
        print("❌ 配置管理器未初始化")
        return
    
    # 1. 发布API配置
    print("1. 发布API配置...")
    api_config_data = {
        "dashscope_api_key": "sk-example-dashscope-key",
        "tencent_appid": "**********",
        "tencent_secret_key": "example-secret-key",
        "tencent_secret_id": "example-secret-id",
        "doubao_appid": "example-doubao-appid",
        "doubao_token": "example-doubao-token",
        "openai_api_key": "sk-example-openai-key",
        "openai_base_url": "https://api.openai.com/v1"
    }
    
    success = config_manager.publish_config(
        "livetalking-api-config",
        api_config_data,
        config_type="json"
    )
    print(f"API配置发布: {'✅ 成功' if success else '❌ 失败'}")
    
    # 2. 发布LLM配置
    print("\n2. 发布LLM配置...")
    llm_config_data = {
        "provider": "dashscope",
        "api_key": "sk-example-dashscope-key",
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "model": "qwen-plus",
        "max_tokens": 8192,
        "temperature": 0.7
    }
    
    success = config_manager.publish_config(
        "livetalking-llm-config",
        llm_config_data,
        config_type="json"
    )
    print(f"LLM配置发布: {'✅ 成功' if success else '❌ 失败'}")
    
    # 3. 发布TTS配置
    print("\n3. 发布TTS配置...")
    tts_config_data = {
        "edgetts": {
            "provider": "edgetts",
            "voice": "zh-CN-YunxiaNeural",
            "speed": 1.0,
            "volume": 1.0,
            "pitch": 1.0
        },
        "tencent": {
            "provider": "tencent",
            "app_id": "**********",
            "secret_key": "example-secret-key",
            "secret_id": "example-secret-id",
            "voice": "101001",
            "speed": 0,
            "volume": 0,
            "pitch": 0
        },
        "doubao": {
            "provider": "doubao",
            "app_id": "example-doubao-appid",
            "api_key": "example-doubao-token",
            "voice": "zh_female_shuangkuaisisi_moon_bigtts",
            "speed": 1.0,
            "volume": 1.0,
            "pitch": 1.0
        }
    }
    
    success = config_manager.publish_config(
        "livetalking-tts-config",
        tts_config_data,
        config_type="json"
    )
    print(f"TTS配置发布: {'✅ 成功' if success else '❌ 失败'}")

def demo_config_reading():
    """演示配置读取"""
    print("\n=== 配置读取演示 ===\n")
    
    config_manager = get_config_manager()
    if not config_manager:
        print("❌ 配置管理器未初始化")
        return
    
    # 清除缓存，强制从Nacos读取
    config_manager.clear_cache()
    
    # 1. 读取API配置
    print("1. 读取API配置...")
    api_config = config_manager.get_api_config()
    print(f"✅ DashScope API Key: {api_config.dashscope_api_key[:20]}..." if api_config.dashscope_api_key else "未配置")
    print(f"✅ 腾讯云配置: App ID={api_config.tencent_appid}")
    print(f"✅ 豆包配置: App ID={api_config.doubao_appid}")
    
    # 2. 读取LLM配置
    print("\n2. 读取LLM配置...")
    llm_config = config_manager.get_llm_config()
    print(f"✅ Provider: {llm_config.provider}")
    print(f"✅ Model: {llm_config.model}")
    print(f"✅ Temperature: {llm_config.temperature}")
    
    # 3. 读取TTS配置
    print("\n3. 读取TTS配置...")
    for provider in ["edgetts", "tencent", "doubao"]:
        tts_config = config_manager.get_tts_config(provider)
        print(f"✅ {provider.upper()}: Voice={tts_config.voice}, Speed={tts_config.speed}")

def demo_fallback_to_env():
    """演示降级到环境变量"""
    print("\n=== 环境变量降级演示 ===\n")
    
    # 设置一些环境变量
    os.environ["DASHSCOPE_API_KEY"] = "sk-env-dashscope-key"
    os.environ["TENCENT_APPID"] = "env-tencent-appid"
    os.environ["DOUBAO_APPID"] = "env-doubao-appid"
    
    # 模拟Nacos不可用的情况
    print("1. 模拟Nacos不可用，测试环境变量降级...")
    
    # 创建一个无效的配置管理器
    try:
        invalid_manager = NacosConfigManager(
            server_addresses="invalid-server:8848",
            namespace="",
            username="nacos",
            password="nacos"
        )
        
        # 尝试获取配置（会降级到环境变量）
        api_config = invalid_manager.get_api_config()
        print(f"✅ 降级成功 - DashScope API Key: {api_config.dashscope_api_key}")
        print(f"✅ 降级成功 - 腾讯云 App ID: {api_config.tencent_appid}")
        print(f"✅ 降级成功 - 豆包 App ID: {api_config.doubao_appid}")
        
    except Exception as e:
        print(f"❌ 降级测试失败: {e}")
    
    # 清理环境变量
    for key in ["DASHSCOPE_API_KEY", "TENCENT_APPID", "DOUBAO_APPID"]:
        if key in os.environ:
            del os.environ[key]

def demo_config_caching():
    """演示配置缓存"""
    print("\n=== 配置缓存演示 ===\n")
    
    config_manager = get_config_manager()
    if not config_manager:
        print("❌ 配置管理器未初始化")
        return
    
    import time
    
    # 1. 第一次读取（从Nacos）
    print("1. 第一次读取配置（从Nacos）...")
    start_time = time.time()
    api_config1 = config_manager.get_api_config()
    first_read_time = time.time() - start_time
    print(f"✅ 第一次读取耗时: {first_read_time:.3f}秒")
    
    # 2. 第二次读取（从缓存）
    print("\n2. 第二次读取配置（从缓存）...")
    start_time = time.time()
    api_config2 = config_manager.get_api_config()
    second_read_time = time.time() - start_time
    print(f"✅ 第二次读取耗时: {second_read_time:.3f}秒")
    
    print(f"✅ 缓存加速比: {first_read_time/second_read_time:.1f}x")
    
    # 3. 清除缓存
    print("\n3. 清除缓存...")
    config_manager.clear_cache()
    print("✅ 缓存已清除")

def demo_integration_with_existing_code():
    """演示与现有代码的集成"""
    print("\n=== 与现有代码集成演示 ===\n")
    
    # 模拟现有的TTS代码
    def simulate_tencent_tts():
        """模拟腾讯TTS的使用"""
        print("模拟腾讯TTS初始化...")
        
        # 原来的代码：
        # self.appid = os.getenv("TENCENT_APPID")
        # self.secret_key = os.getenv("TENCENT_SECRET_KEY")
        # self.secret_id = os.getenv("TENCENT_SECRET_ID")
        
        # 新的代码：
        tts_config = get_tts_config("tencent")
        appid = tts_config.app_id
        secret_key = tts_config.secret_key
        secret_id = tts_config.secret_id
        
        print(f"✅ App ID: {appid}")
        print(f"✅ Secret Key: {secret_key[:10]}..." if secret_key else "未配置")
        print(f"✅ Secret ID: {secret_id[:10]}..." if secret_id else "未配置")
    
    def simulate_llm_usage():
        """模拟LLM的使用"""
        print("\n模拟LLM初始化...")
        
        # 原来的代码：
        # api_key = os.getenv("DASHSCOPE_API_KEY")
        # base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        
        # 新的代码：
        llm_config = get_llm_config()
        api_key = llm_config.api_key
        base_url = llm_config.base_url
        model = llm_config.model
        
        print(f"✅ API Key: {api_key[:20]}..." if api_key else "未配置")
        print(f"✅ Base URL: {base_url}")
        print(f"✅ Model: {model}")
    
    simulate_tencent_tts()
    simulate_llm_usage()

async def main():
    """主函数"""
    print("🚀 Nacos配置管理演示开始\n")
    
    try:
        # 基本用法演示
        demo_basic_usage()
        
        # 配置发布演示
        demo_config_publishing()
        
        # 等待一下让配置生效
        await asyncio.sleep(2)
        
        # 配置读取演示
        demo_config_reading()
        
        # 缓存演示
        demo_config_caching()
        
        # 降级演示
        demo_fallback_to_env()
        
        # 集成演示
        demo_integration_with_existing_code()
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {e}")
    
    print("\n🎉 Nacos配置管理演示完成")

if __name__ == "__main__":
    asyncio.run(main())
