"""
智能打断功能演示
展示如何使用语音指令打断数字人说话
"""
import asyncio
import json
import logging
import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from interrupt_manager import InterruptManager, InterruptConfig, InterruptType
from enhanced_interrupt_handler import integrate_with_nerfreal

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockNerfReal:
    """模拟的NerfReal实例用于演示"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self._speaking = False
        self._message_queue = []
    
    def is_speaking(self) -> bool:
        return self._speaking
    
    def put_msg_txt(self, msg: str, eventpoint=None):
        """模拟数字人说话"""
        self._message_queue.append(msg)
        self._speaking = True
        logger.info(f"数字人开始说话: {msg}")
        
        # 模拟说话过程
        asyncio.create_task(self._simulate_speaking(msg))
    
    async def _simulate_speaking(self, msg: str):
        """模拟说话过程"""
        # 根据文本长度计算说话时间
        speaking_time = len(msg) * 0.1  # 每个字符0.1秒
        await asyncio.sleep(speaking_time)
        
        if self._speaking:  # 如果没有被打断
            self._speaking = False
            logger.info(f"数字人说话结束: {msg}")
    
    def flush_talk(self):
        """打断说话"""
        if self._speaking:
            self._speaking = False
            self._message_queue.clear()
            logger.info("数字人被打断，停止说话")

class InterruptDemo:
    """打断功能演示"""
    
    def __init__(self):
        self.interrupt_manager = None
        self.mock_nerfreals = {}
    
    def setup_interrupt_system(self):
        """设置打断系统"""
        # 创建打断配置
        config = InterruptConfig(
            interrupt_keywords=[
                "停", "停止", "别说了", "够了", "打住", "暂停",
                "stop", "pause", "enough", "shut up",
                "等等", "等一下", "慢着", "hold on"
            ],
            keyword_sensitivity=0.8,
            voice_activity_threshold=0.5,
            voice_activity_duration=1.0,
            max_speaking_duration=10.0,  # 演示用，设置为10秒
            interrupt_delay=0.2,
            smart_interrupt=True,
            sentence_boundary_detection=True
        )
        
        # 初始化打断管理器
        self.interrupt_manager = InterruptManager(config)
        self.interrupt_manager.start()
        
        logger.info("打断系统初始化完成")
    
    def create_mock_session(self, session_id: str):
        """创建模拟会话"""
        mock_nerfreal = MockNerfReal(session_id)
        self.mock_nerfreals[session_id] = mock_nerfreal
        
        # 注册到打断管理器
        self.interrupt_manager.register_session(session_id, mock_nerfreal)
        
        # 添加打断回调
        def on_interrupt(event):
            print(f"🚨 会话 {session_id} 被打断!")
            print(f"   打断类型: {event.interrupt_type.value}")
            if event.trigger_text:
                print(f"   触发词: {event.trigger_text}")
            print(f"   置信度: {event.confidence:.2f}")
            print()
        
        self.interrupt_manager.add_interrupt_callback(session_id, on_interrupt)
        
        logger.info(f"创建模拟会话: {session_id}")
        return mock_nerfreal
    
    async def demo_keyword_interrupt(self):
        """演示关键词打断"""
        print("\n=== 关键词打断演示 ===")
        
        session_id = "demo_session_1"
        nerfreal = self.create_mock_session(session_id)
        
        # 数字人开始长篇说话
        long_text = "这是一段很长的话，我会一直说下去，说很多很多的内容，包括各种各样的信息，直到你打断我为止。"
        nerfreal.put_msg_txt(long_text)
        
        # 等待一会儿
        await asyncio.sleep(2)
        
        # 用户说出打断指令
        print("👤 用户说: '停'")
        is_interrupted = self.interrupt_manager.process_text_input(session_id, "停")
        
        if is_interrupted:
            print("✅ 检测到打断指令，数字人已停止说话")
        else:
            print("❌ 未检测到打断指令")
        
        await asyncio.sleep(1)
    
    async def demo_multiple_keywords(self):
        """演示多种打断关键词"""
        print("\n=== 多种打断关键词演示 ===")
        
        session_id = "demo_session_2"
        nerfreal = self.create_mock_session(session_id)
        
        test_phrases = [
            "停止说话",
            "够了够了",
            "等等，我有问题",
            "hold on",
            "pause please",
            "别说了",
            "打住"
        ]
        
        for i, phrase in enumerate(test_phrases):
            print(f"\n测试 {i+1}: '{phrase}'")
            
            # 数字人开始说话
            nerfreal.put_msg_txt(f"这是第{i+1}次测试，我正在说话...")
            await asyncio.sleep(0.5)
            
            # 测试打断
            is_interrupted = self.interrupt_manager.process_text_input(session_id, phrase)
            
            if is_interrupted:
                print(f"✅ '{phrase}' 成功触发打断")
            else:
                print(f"❌ '{phrase}' 未触发打断")
            
            await asyncio.sleep(1)
    
    async def demo_manual_interrupt(self):
        """演示手动打断"""
        print("\n=== 手动打断演示 ===")
        
        session_id = "demo_session_3"
        nerfreal = self.create_mock_session(session_id)
        
        # 数字人开始说话
        nerfreal.put_msg_txt("我现在开始一段长时间的说话，你可以随时手动打断我。")
        
        # 等待一会儿
        await asyncio.sleep(2)
        
        # 手动打断
        print("🖱️ 执行手动打断")
        self.interrupt_manager.manual_interrupt(session_id)
        
        await asyncio.sleep(1)
    
    async def demo_timeout_interrupt(self):
        """演示超时打断"""
        print("\n=== 超时打断演示 ===")
        
        session_id = "demo_session_4"
        nerfreal = self.create_mock_session(session_id)
        
        # 数字人开始超长说话
        very_long_text = "我现在要说一段非常非常长的话，" * 50
        nerfreal.put_msg_txt(very_long_text)
        
        print("⏰ 等待超时打断 (10秒后触发)...")
        
        # 等待超时
        for i in range(12):
            await asyncio.sleep(1)
            if not nerfreal.is_speaking():
                print(f"✅ 在第{i+1}秒时触发了超时打断")
                break
        else:
            print("❌ 超时打断未触发")
    
    async def demo_smart_interrupt(self):
        """演示智能打断"""
        print("\n=== 智能打断演示 ===")
        
        session_id = "demo_session_5"
        nerfreal = self.create_mock_session(session_id)
        
        # 测试句子边界检测
        print("测试句子边界检测...")
        
        # 数字人说话，包含句子边界
        nerfreal.put_msg_txt("这是第一句话。这是第二句话！这是第三句话？")
        
        await asyncio.sleep(1)
        
        # 在句子中间尝试打断
        print("👤 在句子中间说: '停'")
        self.interrupt_manager.process_text_input(session_id, "停")
        
        await asyncio.sleep(2)
    
    async def demo_voice_activity_interrupt(self):
        """演示语音活动打断"""
        print("\n=== 语音活动打断演示 ===")
        
        session_id = "demo_session_6"
        nerfreal = self.create_mock_session(session_id)
        
        # 数字人开始说话
        nerfreal.put_msg_txt("我正在说话，如果你开始说话，我会被打断。")
        
        await asyncio.sleep(1)
        
        # 模拟用户开始说话 (高音量音频)
        print("🎤 模拟用户开始说话 (高音量音频)")
        
        # 创建模拟音频数据
        import numpy as np
        
        # 模拟高音量音频数据
        for i in range(20):  # 持续1秒以上
            audio_chunk = np.random.randint(-16000, 16000, 320, dtype=np.int16).tobytes()
            self.interrupt_manager.process_audio_input(session_id, audio_chunk, volume_level=0.8)
            await asyncio.sleep(0.05)  # 50ms间隔
        
        await asyncio.sleep(1)
    
    async def run_all_demos(self):
        """运行所有演示"""
        print("🎭 智能打断功能演示开始")
        print("=" * 50)
        
        # 设置打断系统
        self.setup_interrupt_system()
        
        # 运行各种演示
        await self.demo_keyword_interrupt()
        await self.demo_multiple_keywords()
        await self.demo_manual_interrupt()
        await self.demo_timeout_interrupt()
        await self.demo_smart_interrupt()
        await self.demo_voice_activity_interrupt()
        
        print("\n" + "=" * 50)
        print("🎉 所有演示完成!")
        
        # 清理
        self.interrupt_manager.stop()

async def interactive_demo():
    """交互式演示"""
    print("\n🎮 交互式打断演示")
    print("输入以下命令:")
    print("  'start' - 让数字人开始说话")
    print("  '停' 或其他打断词 - 打断数字人")
    print("  'manual' - 手动打断")
    print("  'quit' - 退出")
    print()
    
    demo = InterruptDemo()
    demo.setup_interrupt_system()
    
    session_id = "interactive_session"
    nerfreal = demo.create_mock_session(session_id)
    
    while True:
        user_input = input("👤 请输入: ").strip()
        
        if user_input.lower() == 'quit':
            break
        elif user_input.lower() == 'start':
            nerfreal.put_msg_txt("我现在开始说话了，这是一段很长的内容，你可以随时打断我。我会一直说下去，直到你说出打断指令为止。")
            print("🤖 数字人开始说话...")
        elif user_input.lower() == 'manual':
            demo.interrupt_manager.manual_interrupt(session_id)
            print("🚨 执行手动打断")
        else:
            is_interrupted = demo.interrupt_manager.process_text_input(session_id, user_input)
            if is_interrupted:
                print(f"🚨 检测到打断指令: '{user_input}'")
            else:
                print(f"💬 普通消息: '{user_input}'")
        
        await asyncio.sleep(0.1)
    
    demo.interrupt_manager.stop()
    print("👋 演示结束")

async def main():
    """主函数"""
    print("选择演示模式:")
    print("1. 自动演示所有功能")
    print("2. 交互式演示")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        demo = InterruptDemo()
        await demo.run_all_demos()
    elif choice == "2":
        await interactive_demo()
    else:
        print("无效选择")

if __name__ == "__main__":
    asyncio.run(main())
