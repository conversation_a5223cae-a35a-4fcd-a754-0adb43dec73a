#!/usr/bin/env python3
"""
Nacos配置自动刷新演示
演示配置的自动刷新和监听功能
"""
import sys
import os
import json
import time
import asyncio
import logging
from threading import Event

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from multi_session_enhancement.nacos_config_manager_v2 import (
    NacosConfigManagerV2, ConfigChangeListener,
    init_config_manager_v2, get_config_manager_v2, 
    get_api_config_v2, get_llm_config_v2, get_tts_config_v2
)
from nacos_service import init_nacos_client

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConfigMonitor:
    """配置监控器"""
    
    def __init__(self):
        self.config_changes = []
        self.change_event = Event()
    
    def on_api_config_change(self, data_id: str, new_config: dict):
        """API配置变更回调"""
        logger.info(f"🔄 API配置已更新: {data_id}")
        logger.info(f"   DashScope Key: {new_config.get('dashscope_api_key', '')[:20]}...")
        logger.info(f"   腾讯云 App ID: {new_config.get('tencent_appid', '')}")
        
        self.config_changes.append({
            'type': 'api_config',
            'data_id': data_id,
            'timestamp': time.time(),
            'config': new_config
        })
        self.change_event.set()
    
    def on_llm_config_change(self, data_id: str, new_config: dict):
        """LLM配置变更回调"""
        logger.info(f"🔄 LLM配置已更新: {data_id}")
        logger.info(f"   Provider: {new_config.get('provider', '')}")
        logger.info(f"   Model: {new_config.get('model', '')}")
        logger.info(f"   Temperature: {new_config.get('temperature', '')}")
        
        self.config_changes.append({
            'type': 'llm_config',
            'data_id': data_id,
            'timestamp': time.time(),
            'config': new_config
        })
        self.change_event.set()
    
    def on_tts_config_change(self, data_id: str, new_config: dict):
        """TTS配置变更回调"""
        logger.info(f"🔄 TTS配置已更新: {data_id}")
        for provider, config in new_config.items():
            logger.info(f"   {provider.upper()}: Voice={config.get('voice', '')}")
        
        self.config_changes.append({
            'type': 'tts_config',
            'data_id': data_id,
            'timestamp': time.time(),
            'config': new_config
        })
        self.change_event.set()

def demo_auto_refresh_setup():
    """演示自动刷新设置"""
    print("=== Nacos配置自动刷新演示 ===\n")
    
    # 1. 初始化Nacos客户端
    print("1. 初始化Nacos客户端...")
    nacos_client = init_nacos_client(
        server_addresses="127.0.0.1:8848",
        namespace="",
        username="nacos",
        password="nacos"
    )
    
    if not nacos_client:
        print("❌ Nacos客户端初始化失败，请检查Nacos服务是否启动")
        return None
    
    print("✅ Nacos客户端初始化成功")
    
    # 2. 初始化配置管理器V2（启用自动刷新）
    print("\n2. 初始化配置管理器V2（启用自动刷新）...")
    config_manager = init_config_manager_v2(
        nacos_client,
        enable_auto_refresh=True,
        refresh_interval=10  # 10秒刷新一次
    )
    print("✅ 配置管理器V2初始化成功")
    print(f"   自动刷新: {'启用' if config_manager.enable_auto_refresh else '禁用'}")
    print(f"   刷新间隔: {config_manager.refresh_interval}秒")
    
    return config_manager

def demo_config_listeners(config_manager: NacosConfigManagerV2):
    """演示配置监听器"""
    print("\n3. 设置配置变更监听器...")
    
    # 创建配置监控器
    monitor = ConfigMonitor()
    
    # 添加监听器
    api_listener = ConfigChangeListener(monitor.on_api_config_change)
    llm_listener = ConfigChangeListener(monitor.on_llm_config_change)
    tts_listener = ConfigChangeListener(monitor.on_tts_config_change)
    
    config_manager.add_config_listener("livetalking-api-config", api_listener)
    config_manager.add_config_listener("livetalking-llm-config", llm_listener)
    config_manager.add_config_listener("livetalking-tts-config", tts_listener)
    
    print("✅ 配置监听器设置完成")
    
    return monitor

def demo_initial_config_read():
    """演示初始配置读取"""
    print("\n4. 读取初始配置...")
    
    # 读取API配置
    api_config = get_api_config_v2()
    print(f"✅ API配置: DashScope Key={'已设置' if api_config.dashscope_api_key else '未设置'}")
    
    # 读取LLM配置
    llm_config = get_llm_config_v2()
    print(f"✅ LLM配置: Provider={llm_config.provider}, Model={llm_config.model}")
    
    # 读取TTS配置
    for provider in ["edgetts", "tencent", "doubao"]:
        tts_config = get_tts_config_v2(provider)
        print(f"✅ {provider.upper()}配置: Voice={tts_config.voice}")

def demo_config_update(config_manager: NacosConfigManagerV2):
    """演示配置更新"""
    print("\n5. 演示配置更新...")
    
    # 更新API配置
    print("更新API配置...")
    new_api_config = {
        "dashscope_api_key": f"sk-updated-key-{int(time.time())}",
        "tencent_appid": f"updated-appid-{int(time.time())}",
        "tencent_secret_key": "updated-secret-key",
        "tencent_secret_id": "updated-secret-id",
        "doubao_appid": "updated-doubao-appid",
        "doubao_token": "updated-doubao-token"
    }
    
    success = config_manager.publish_config(
        "livetalking-api-config",
        new_api_config,
        config_type="json"
    )
    print(f"API配置更新: {'✅ 成功' if success else '❌ 失败'}")
    
    # 等待一下
    time.sleep(2)
    
    # 更新LLM配置
    print("\n更新LLM配置...")
    new_llm_config = {
        "provider": "dashscope",
        "api_key": f"sk-updated-llm-key-{int(time.time())}",
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "model": "qwen-turbo",  # 改变模型
        "max_tokens": 4096,     # 改变参数
        "temperature": 0.8      # 改变温度
    }
    
    success = config_manager.publish_config(
        "livetalking-llm-config",
        new_llm_config,
        config_type="json"
    )
    print(f"LLM配置更新: {'✅ 成功' if success else '❌ 失败'}")

def demo_wait_for_changes(monitor: ConfigMonitor, timeout: int = 60):
    """等待配置变更"""
    print(f"\n6. 等待配置自动刷新（最多等待{timeout}秒）...")
    print("   提示: 你可以在Nacos控制台手动修改配置来测试自动刷新")
    
    start_time = time.time()
    changes_detected = 0
    
    while time.time() - start_time < timeout:
        if monitor.change_event.wait(timeout=5):
            changes_detected += 1
            monitor.change_event.clear()
            
            print(f"\n🎉 检测到第{changes_detected}次配置变更!")
            
            # 显示最新的配置
            print("当前配置状态:")
            api_config = get_api_config_v2()
            llm_config = get_llm_config_v2()
            
            print(f"  API: DashScope Key={api_config.dashscope_api_key[:20]}...")
            print(f"  LLM: Model={llm_config.model}, Temperature={llm_config.temperature}")
            
            if changes_detected >= 2:  # 检测到足够的变更后退出
                break
        else:
            print(".", end="", flush=True)
    
    print(f"\n总共检测到 {changes_detected} 次配置变更")
    
    # 显示变更历史
    if monitor.config_changes:
        print("\n配置变更历史:")
        for i, change in enumerate(monitor.config_changes, 1):
            timestamp = time.strftime("%H:%M:%S", time.localtime(change['timestamp']))
            print(f"  {i}. [{timestamp}] {change['type']} - {change['data_id']}")

def demo_cache_info(config_manager: NacosConfigManagerV2):
    """演示缓存信息"""
    print("\n7. 缓存信息:")
    cache_info = config_manager.get_cache_info()
    print(f"  缓存大小: {cache_info['cache_size']}")
    print(f"  缓存配置: {cache_info['cached_configs']}")
    print(f"  缓存超时: {cache_info['cache_timeout']}秒")

def demo_force_refresh(config_manager: NacosConfigManagerV2):
    """演示强制刷新"""
    print("\n8. 演示强制刷新...")
    config_manager.force_refresh()
    print("✅ 强制刷新完成")

async def main():
    """主函数"""
    print("🚀 Nacos配置自动刷新演示开始\n")
    
    try:
        # 设置自动刷新
        config_manager = demo_auto_refresh_setup()
        if not config_manager:
            return
        
        # 设置监听器
        monitor = demo_config_listeners(config_manager)
        
        # 读取初始配置
        demo_initial_config_read()
        
        # 更新配置（触发自动刷新）
        demo_config_update(config_manager)
        
        # 等待配置变更
        demo_wait_for_changes(monitor, timeout=30)
        
        # 显示缓存信息
        demo_cache_info(config_manager)
        
        # 强制刷新
        demo_force_refresh(config_manager)
        
        # 停止自动刷新
        print("\n9. 停止自动刷新...")
        config_manager.stop_auto_refresh()
        print("✅ 自动刷新已停止")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  演示被用户中断")
    except Exception as e:
        logger.error(f"演示过程中出现错误: {e}")
    
    print("\n🎉 Nacos配置自动刷新演示完成")
    print("\n总结:")
    print("✅ 配置可以自动从Nacos刷新")
    print("✅ 支持配置变更监听")
    print("✅ 提供缓存机制提高性能")
    print("✅ 支持强制刷新")
    print("✅ 支持优雅停止")

if __name__ == "__main__":
    asyncio.run(main())
