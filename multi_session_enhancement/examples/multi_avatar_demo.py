"""
多形象演示 - 展示如何使用不同的形象和声音创建多个会话
"""
import asyncio
import aiohttp
import json
import time

class MultiAvatarDemo:
    """多形象演示类"""
    
    def __init__(self, base_url: str = "http://localhost:8010"):
        self.base_url = base_url
        self.sessions = []
    
    async def create_session_with_config(self, session_config: dict, offer_sdp: str = None):
        """创建带配置的会话"""
        # 如果没有提供SDP，使用示例SDP
        if not offer_sdp:
            offer_sdp = self._get_sample_offer_sdp()
        
        data = {
            "sdp": offer_sdp,
            "type": "offer",
            "session_config": session_config
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.base_url}/offer_with_config", json=data) as response:
                result = await response.json()
                if result.get("code") == 0:
                    session_id = result.get("session_id")
                    self.sessions.append(session_id)
                    print(f"Created session {session_id} with config: {session_config}")
                    return session_id, result
                else:
                    print(f"Failed to create session: {result.get('msg')}")
                    return None, result
    
    async def update_session_config(self, session_id: int, config_updates: dict):
        """更新会话配置"""
        data = {
            "session_id": session_id,
            "config": config_updates
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.base_url}/update_session_config", json=data) as response:
                result = await response.json()
                if result.get("code") == 0:
                    print(f"Updated session {session_id} config: {config_updates}")
                    return result
                else:
                    print(f"Failed to update session config: {result.get('msg')}")
                    return result
    
    async def send_message(self, session_id: int, message: str, msg_type: str = "chat"):
        """向会话发送消息"""
        data = {
            "sessionid": session_id,
            "text": message,
            "type": msg_type
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.base_url}/human", json=data) as response:
                result = await response.json()
                if result.get("code") == 0:
                    print(f"Sent message to session {session_id}: {message}")
                else:
                    print(f"Failed to send message: {result.get('msg')}")
                return result
    
    async def list_sessions(self):
        """列出所有会话"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.base_url}/sessions") as response:
                result = await response.json()
                if result.get("code") == 0:
                    sessions = result.get("sessions", {})
                    print(f"Active sessions: {len(sessions)}")
                    for sid, config in sessions.items():
                        print(f"  Session {sid}: {config['avatar_id']} - {config['voice']}")
                    return sessions
                else:
                    print(f"Failed to list sessions: {result.get('msg')}")
                    return {}
    
    async def get_resource_stats(self):
        """获取资源统计"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.base_url}/resource_stats") as response:
                result = await response.json()
                if result.get("code") == 0:
                    stats = result.get("stats", {})
                    print("Resource Statistics:")
                    print(f"  Models cached: {stats.get('models', {}).get('cached', 0)}")
                    print(f"  Avatars cached: {stats.get('avatars', {}).get('cached', 0)}")
                    return stats
                else:
                    print(f"Failed to get resource stats: {result.get('msg')}")
                    return {}
    
    def _get_sample_offer_sdp(self):
        """获取示例SDP（实际使用时需要真实的WebRTC SDP）"""
        return """v=0
o=- 0 0 IN IP4 127.0.0.1
s=-
c=IN IP4 127.0.0.1
t=0 0
m=video 9 UDP/TLS/RTP/SAVPF 96
a=rtpmap:96 H264/90000
a=sendrecv
m=audio 9 UDP/TLS/RTP/SAVPF 111
a=rtpmap:111 opus/48000/2
a=sendrecv"""
    
    async def run_demo(self):
        """运行演示"""
        print("=== Multi-Avatar Demo ===")
        
        # 定义不同的会话配置
        configs = [
            {
                "name": "Female Voice 1",
                "config": {
                    "avatar_id": "female_avatar_1",
                    "model": "musetalk",
                    "tts": "edgetts",
                    "voice": "zh-CN-XiaoxiaoNeural"
                }
            },
            {
                "name": "Male Voice 1",
                "config": {
                    "avatar_id": "male_avatar_1",
                    "model": "musetalk",
                    "tts": "edgetts",
                    "voice": "zh-CN-YunxiNeural"
                }
            },
            {
                "name": "Wav2Lip Model",
                "config": {
                    "avatar_id": "wav2lip_avatar_1",
                    "model": "wav2lip",
                    "tts": "edgetts",
                    "voice": "zh-CN-YunjianNeural"
                }
            }
        ]
        
        # 创建多个会话
        print("\n1. Creating sessions with different configurations...")
        session_ids = []
        for config_info in configs:
            session_id, result = await self.create_session_with_config(config_info["config"])
            if session_id:
                session_ids.append((session_id, config_info["name"]))
                await asyncio.sleep(1)  # 避免创建过快
        
        # 列出会话
        print("\n2. Listing all sessions...")
        await self.list_sessions()
        
        # 获取资源统计
        print("\n3. Getting resource statistics...")
        await self.get_resource_stats()
        
        # 向每个会话发送消息
        print("\n4. Sending messages to each session...")
        messages = [
            "你好，我是第一个数字人",
            "大家好，我是第二个数字人",
            "欢迎使用多会话功能"
        ]
        
        for i, (session_id, name) in enumerate(session_ids):
            if i < len(messages):
                print(f"Sending to {name} (Session {session_id}): {messages[i]}")
                await self.send_message(session_id, messages[i])
                await asyncio.sleep(2)
        
        # 动态更新会话配置
        print("\n5. Updating session configurations...")
        if session_ids:
            session_id, name = session_ids[0]
            new_config = {"voice": "zh-CN-YunyangNeural"}
            print(f"Updating {name} voice to {new_config['voice']}")
            await self.update_session_config(session_id, new_config)
        
        # 再次列出会话查看更新
        print("\n6. Listing sessions after update...")
        await self.list_sessions()
        
        print("\n=== Demo completed ===")

async def main():
    """主函数"""
    demo = MultiAvatarDemo()
    await demo.run_demo()

if __name__ == "__main__":
    asyncio.run(main())
