"""
SenseVoice部署和使用示例
演示如何部署SenseVoice模型并集成到LiveTalking
"""
import asyncio
import json
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from asr_config_manager import ASRConfigManager, ASRConfig

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SenseVoiceDeploymentExample:
    """SenseVoice部署示例"""
    
    def __init__(self):
        self.config = None
        self.asr_manager = None
    
    def create_sensevoice_config(self, server_url: str = "http://localhost:8000"):
        """创建SenseVoice配置"""
        config = ASRConfig(
            provider="sensevoice",
            enabled=True,
            language="zh",
            sample_rate=16000,
            format="wav",
            sensevoice_api_url=server_url,
            sensevoice_model="sensevoice-small",
            sensevoice_language="auto",
            sensevoice_use_itn=True
        )
        
        logger.info(f"创建SenseVoice配置: {server_url}")
        return config
    
    def create_openai_whisper_config(self, api_key: str, base_url: str = "https://api.openai.com/v1"):
        """创建OpenAI Whisper配置"""
        config = ASRConfig(
            provider="openai_whisper",
            enabled=True,
            language="zh",
            sample_rate=16000,
            format="wav",
            openai_api_key=api_key,
            openai_base_url=base_url,
            openai_model="whisper-1",
            openai_temperature=0.0,
            openai_language="zh"
        )
        
        logger.info(f"创建OpenAI Whisper配置: {base_url}")
        return config
    
    def create_custom_whisper_config(self, server_url: str, model_name: str = "large-v3"):
        """创建自定义Whisper服务配置"""
        config = ASRConfig(
            provider="openai_whisper",
            enabled=True,
            language="zh",
            sample_rate=16000,
            format="wav",
            openai_api_key="dummy-key",  # 自部署服务通常不需要真实API key
            openai_base_url=server_url,
            openai_model=model_name,
            openai_temperature=0.0,
            openai_language="zh"
        )
        
        logger.info(f"创建自定义Whisper配置: {server_url}")
        return config
    
    async def test_asr_service(self, config: ASRConfig):
        """测试ASR服务"""
        try:
            # 创建ASR管理器
            asr_manager = ASRConfigManager("test_asr_config.json")
            asr_manager.config = config
            asr_manager.save_config()
            
            # 创建测试音频数据 (1秒的静音WAV)
            test_audio = self.create_test_wav()
            
            # 测试转录
            logger.info("开始测试ASR服务...")
            result = await asr_manager.transcribe_audio(test_audio, "wav")
            
            if result:
                logger.info(f"ASR测试成功: {result}")
                return True
            else:
                logger.warning("ASR测试返回空结果")
                return False
                
        except Exception as e:
            logger.error(f"ASR测试失败: {e}")
            return False
        finally:
            await asr_manager.close()
    
    def create_test_wav(self, duration: float = 1.0, sample_rate: int = 16000) -> bytes:
        """创建测试WAV文件"""
        import wave
        import io
        import numpy as np
        
        # 生成静音数据
        samples = int(duration * sample_rate)
        audio_data = np.zeros(samples, dtype=np.int16)
        
        # 创建WAV文件
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        wav_buffer.seek(0)
        return wav_buffer.read()
    
    def generate_docker_compose(self, output_file: str = "docker-compose-asr.yml"):
        """生成Docker Compose配置"""
        docker_compose = """
version: '3.8'

services:
  # SenseVoice服务
  sensevoice:
    image: sensevoice:latest
    container_name: sensevoice-asr
    ports:
      - "8000:8000"
    environment:
      - MODEL_NAME=sensevoice-small
      - DEVICE=cpu
    volumes:
      - ./models:/app/models
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Faster-Whisper服务 (OpenAI兼容)
  faster-whisper:
    image: fedirz/faster-whisper-server:latest
    container_name: faster-whisper-asr
    ports:
      - "8001:8000"
    environment:
      - MODEL=large-v3
      - DEVICE=cpu
    volumes:
      - ./whisper-models:/app/models
    restart: unless-stopped

  # LocalAI (支持多种模型)
  localai:
    image: localai/localai:latest
    container_name: localai-asr
    ports:
      - "8002:8080"
    environment:
      - MODELS_PATH=/models
    volumes:
      - ./localai-models:/models
    restart: unless-stopped

  # LiveTalking应用
  livetalking:
    build: .
    container_name: livetalking-app
    ports:
      - "8080:8080"
    environment:
      - ASR_PROVIDER=sensevoice
      - SENSEVOICE_API_URL=http://sensevoice:8000
    depends_on:
      - sensevoice
    restart: unless-stopped
"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(docker_compose.strip())
        
        logger.info(f"Docker Compose配置已生成: {output_file}")
    
    def generate_deployment_script(self, output_file: str = "deploy_asr.sh"):
        """生成部署脚本"""
        script = """#!/bin/bash

# ASR服务部署脚本

echo "开始部署ASR服务..."

# 1. 创建目录
mkdir -p models whisper-models localai-models

# 2. 下载SenseVoice模型 (示例)
echo "下载SenseVoice模型..."
# wget -O models/sensevoice-small.bin https://your-model-url/sensevoice-small.bin

# 3. 下载Whisper模型 (示例)
echo "下载Whisper模型..."
# wget -O whisper-models/large-v3.bin https://your-model-url/large-v3.bin

# 4. 启动服务
echo "启动Docker服务..."
docker-compose -f docker-compose-asr.yml up -d

# 5. 等待服务启动
echo "等待服务启动..."
sleep 30

# 6. 测试服务
echo "测试ASR服务..."

# 测试SenseVoice
curl -X POST http://localhost:8000/v1/audio/transcriptions \\
  -F "audio=@test.wav" \\
  -F "model=sensevoice-small" || echo "SenseVoice测试失败"

# 测试Faster-Whisper
curl -X POST http://localhost:8001/v1/audio/transcriptions \\
  -F "file=@test.wav" \\
  -F "model=large-v3" || echo "Faster-Whisper测试失败"

echo "ASR服务部署完成！"
echo "SenseVoice: http://localhost:8000"
echo "Faster-Whisper: http://localhost:8001"
echo "LocalAI: http://localhost:8002"
"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(script.strip())
        
        # 设置执行权限
        os.chmod(output_file, 0o755)
        
        logger.info(f"部署脚本已生成: {output_file}")

async def main():
    """主函数 - 演示ASR配置和测试"""
    example = SenseVoiceDeploymentExample()
    
    print("=== ASR服务配置和部署示例 ===")
    
    # 1. 生成部署文件
    print("\n1. 生成部署配置文件...")
    example.generate_docker_compose()
    example.generate_deployment_script()
    
    # 2. 创建不同的ASR配置
    print("\n2. 创建ASR配置示例...")
    
    # SenseVoice配置
    sensevoice_config = example.create_sensevoice_config("http://localhost:8000")
    print(f"SenseVoice配置: {sensevoice_config.sensevoice_api_url}")
    
    # 自定义Whisper配置
    custom_whisper_config = example.create_custom_whisper_config(
        "http://localhost:8001/v1", 
        "large-v3"
    )
    print(f"自定义Whisper配置: {custom_whisper_config.openai_base_url}")
    
    # 3. 测试配置 (如果服务可用)
    print("\n3. 测试ASR配置...")
    
    configs_to_test = [
        ("SenseVoice", sensevoice_config),
        ("自定义Whisper", custom_whisper_config)
    ]
    
    for name, config in configs_to_test:
        print(f"\n测试 {name} 配置...")
        try:
            success = await example.test_asr_service(config)
            if success:
                print(f"✅ {name} 配置测试成功")
            else:
                print(f"❌ {name} 配置测试失败")
        except Exception as e:
            print(f"❌ {name} 配置测试异常: {e}")
    
    # 4. 显示使用说明
    print("\n=== 使用说明 ===")
    print("1. 运行 ./deploy_asr.sh 部署ASR服务")
    print("2. 修改 asr_config.json 选择ASR提供商")
    print("3. 重启LiveTalking应用以应用新配置")
    print("\n支持的ASR服务:")
    print("- SenseVoice: http://localhost:8000")
    print("- Faster-Whisper: http://localhost:8001")
    print("- LocalAI: http://localhost:8002")
    print("- OpenAI Whisper API: https://api.openai.com/v1")

if __name__ == "__main__":
    asyncio.run(main())
