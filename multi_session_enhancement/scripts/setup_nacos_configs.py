#!/usr/bin/env python3
"""
Nacos配置初始化脚本
自动创建LiveTalking所需的Nacos配置
"""
import sys
import os
import json
import argparse
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from multi_session_enhancement.nacos_config_manager import NacosConfigManager
from nacos_service import init_nacos_client

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_sample_configs():
    """加载示例配置"""
    config_file = os.path.join(
        os.path.dirname(__file__), 
        "..", 
        "configs", 
        "nacos_config_samples.json"
    )
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载示例配置失败: {e}")
        return None

def setup_api_config(config_manager: NacosConfigManager, custom_config: dict = None):
    """设置API配置"""
    logger.info("设置API配置...")
    
    if custom_config:
        api_config = custom_config
    else:
        # 使用示例配置
        samples = load_sample_configs()
        if not samples:
            return False
        api_config = samples["api_config_sample"]["config"]
    
    success = config_manager.publish_config(
        "livetalking-api-config",
        api_config,
        config_type="json"
    )
    
    if success:
        logger.info("✅ API配置设置成功")
    else:
        logger.error("❌ API配置设置失败")
    
    return success

def setup_llm_config(config_manager: NacosConfigManager, custom_config: dict = None):
    """设置LLM配置"""
    logger.info("设置LLM配置...")
    
    if custom_config:
        llm_config = custom_config
    else:
        samples = load_sample_configs()
        if not samples:
            return False
        llm_config = samples["llm_config_sample"]["config"]
    
    success = config_manager.publish_config(
        "livetalking-llm-config",
        llm_config,
        config_type="json"
    )
    
    if success:
        logger.info("✅ LLM配置设置成功")
    else:
        logger.error("❌ LLM配置设置失败")
    
    return success

def setup_tts_config(config_manager: NacosConfigManager, custom_config: dict = None):
    """设置TTS配置"""
    logger.info("设置TTS配置...")
    
    if custom_config:
        tts_config = custom_config
    else:
        samples = load_sample_configs()
        if not samples:
            return False
        tts_config = samples["tts_config_sample"]["config"]
    
    success = config_manager.publish_config(
        "livetalking-tts-config",
        tts_config,
        config_type="json"
    )
    
    if success:
        logger.info("✅ TTS配置设置成功")
    else:
        logger.error("❌ TTS配置设置失败")
    
    return success

def setup_from_env_vars(config_manager: NacosConfigManager):
    """从环境变量创建配置"""
    logger.info("从环境变量创建配置...")
    
    # API配置
    api_config = {
        "dashscope_api_key": os.getenv("DASHSCOPE_API_KEY", ""),
        "tencent_appid": os.getenv("TENCENT_APPID", ""),
        "tencent_secret_key": os.getenv("TENCENT_SECRET_KEY", ""),
        "tencent_secret_id": os.getenv("TENCENT_SECRET_ID", ""),
        "doubao_appid": os.getenv("DOUBAO_APPID", ""),
        "doubao_token": os.getenv("DOUBAO_TOKEN", ""),
        "openai_api_key": os.getenv("OPENAI_API_KEY", ""),
        "openai_base_url": os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    }
    
    # LLM配置
    llm_config = {
        "provider": "dashscope",
        "api_key": os.getenv("DASHSCOPE_API_KEY", ""),
        "base_url": os.getenv("LLM_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        "model": os.getenv("LLM_MODEL", "qwen-plus"),
        "max_tokens": int(os.getenv("LLM_MAX_TOKENS", "8192")),
        "temperature": float(os.getenv("LLM_TEMPERATURE", "0.7"))
    }
    
    # TTS配置
    tts_config = {
        "edgetts": {
            "provider": "edgetts",
            "voice": "zh-CN-YunxiaNeural",
            "speed": 1.0,
            "volume": 1.0,
            "pitch": 1.0
        },
        "tencent": {
            "provider": "tencent",
            "app_id": os.getenv("TENCENT_APPID", ""),
            "secret_key": os.getenv("TENCENT_SECRET_KEY", ""),
            "secret_id": os.getenv("TENCENT_SECRET_ID", ""),
            "voice": "101001",
            "speed": 0,
            "volume": 0,
            "pitch": 0
        },
        "doubao": {
            "provider": "doubao",
            "app_id": os.getenv("DOUBAO_APPID", ""),
            "api_key": os.getenv("DOUBAO_TOKEN", ""),
            "voice": "zh_female_shuangkuaisisi_moon_bigtts",
            "speed": 1.0,
            "volume": 1.0,
            "pitch": 1.0
        }
    }
    
    # 发布配置
    results = []
    results.append(setup_api_config(config_manager, api_config))
    results.append(setup_llm_config(config_manager, llm_config))
    results.append(setup_tts_config(config_manager, tts_config))
    
    return all(results)

def verify_configs(config_manager: NacosConfigManager):
    """验证配置"""
    logger.info("验证配置...")
    
    try:
        # 验证API配置
        api_config = config_manager.get_api_config()
        logger.info(f"✅ API配置验证成功: DashScope Key={'已设置' if api_config.dashscope_api_key else '未设置'}")
        
        # 验证LLM配置
        llm_config = config_manager.get_llm_config()
        logger.info(f"✅ LLM配置验证成功: Provider={llm_config.provider}, Model={llm_config.model}")
        
        # 验证TTS配置
        for provider in ["edgetts", "tencent", "doubao"]:
            tts_config = config_manager.get_tts_config(provider)
            logger.info(f"✅ {provider.upper()}配置验证成功: Voice={tts_config.voice}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 配置验证失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Nacos配置初始化脚本")
    parser.add_argument("--server", default="127.0.0.1:8848", help="Nacos服务器地址")
    parser.add_argument("--namespace", default="", help="Nacos命名空间")
    parser.add_argument("--username", default="nacos", help="Nacos用户名")
    parser.add_argument("--password", default="nacos", help="Nacos密码")
    parser.add_argument("--from-env", action="store_true", help="从环境变量创建配置")
    parser.add_argument("--config-file", help="自定义配置文件路径")
    parser.add_argument("--verify-only", action="store_true", help="仅验证现有配置")
    
    args = parser.parse_args()
    
    logger.info("🚀 开始Nacos配置初始化")
    
    # 初始化Nacos客户端
    logger.info(f"连接Nacos服务器: {args.server}")
    nacos_client = init_nacos_client(
        server_addresses=args.server,
        namespace=args.namespace,
        username=args.username,
        password=args.password
    )
    
    if not nacos_client:
        logger.error("❌ Nacos客户端初始化失败")
        return 1
    
    # 创建配置管理器
    config_manager = NacosConfigManager(nacos_client)
    
    if args.verify_only:
        # 仅验证配置
        success = verify_configs(config_manager)
        return 0 if success else 1
    
    # 设置配置
    if args.from_env:
        # 从环境变量创建
        success = setup_from_env_vars(config_manager)
    elif args.config_file:
        # 从自定义文件创建
        try:
            with open(args.config_file, 'r', encoding='utf-8') as f:
                custom_configs = json.load(f)
            
            results = []
            if "api_config" in custom_configs:
                results.append(setup_api_config(config_manager, custom_configs["api_config"]))
            if "llm_config" in custom_configs:
                results.append(setup_llm_config(config_manager, custom_configs["llm_config"]))
            if "tts_config" in custom_configs:
                results.append(setup_tts_config(config_manager, custom_configs["tts_config"]))
            
            success = all(results)
        except Exception as e:
            logger.error(f"❌ 读取自定义配置文件失败: {e}")
            return 1
    else:
        # 使用示例配置
        results = []
        results.append(setup_api_config(config_manager))
        results.append(setup_llm_config(config_manager))
        results.append(setup_tts_config(config_manager))
        success = all(results)
    
    if success:
        logger.info("✅ 配置设置完成，开始验证...")
        verify_success = verify_configs(config_manager)
        if verify_success:
            logger.info("🎉 Nacos配置初始化完成！")
            return 0
        else:
            logger.error("❌ 配置验证失败")
            return 1
    else:
        logger.error("❌ 配置设置失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
