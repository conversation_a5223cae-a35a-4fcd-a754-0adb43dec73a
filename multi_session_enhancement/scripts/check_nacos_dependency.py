#!/usr/bin/env python3
"""
检查Nacos依赖是否正确安装
"""
import sys

def check_nacos_sdk():
    """检查nacos-sdk-python是否安装"""
    try:
        import nacos
        print("✅ nacos-sdk-python 已安装")
        print(f"   版本: {nacos.__version__ if hasattr(nacos, '__version__') else '未知'}")
        return True
    except ImportError as e:
        print("❌ nacos-sdk-python 未安装")
        print(f"   错误: {e}")
        print("   请运行: pip install nacos-sdk-python")
        return False

def check_other_dependencies():
    """检查其他相关依赖"""
    dependencies = [
        ("yaml", "pyyaml"),
        ("requests", "requests"),
        ("json", "内置模块"),
        ("logging", "内置模块"),
        ("asyncio", "内置模块"),
        ("dataclasses", "内置模块"),
        ("typing", "内置模块")
    ]
    
    print("\n检查其他依赖:")
    all_ok = True
    
    for module_name, package_name in dependencies:
        try:
            __import__(module_name)
            print(f"✅ {module_name} ({package_name})")
        except ImportError:
            print(f"❌ {module_name} ({package_name}) - 请安装: pip install {package_name}")
            all_ok = False
    
    return all_ok

def test_nacos_connection():
    """测试Nacos连接"""
    try:
        import nacos
        
        print("\n测试Nacos客户端创建:")
        
        # 创建客户端（不连接真实服务器）
        client = nacos.NacosClient(
            server_addresses="127.0.0.1:8848",
            namespace="",
            username="nacos",
            password="nacos"
        )
        
        print("✅ Nacos客户端创建成功")
        print("   注意: 这只是测试客户端创建，实际连接需要Nacos服务器运行")
        return True
        
    except Exception as e:
        print(f"❌ Nacos客户端创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 检查Nacos相关依赖\n")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("⚠️  警告: 建议使用Python 3.7+")
    
    print()
    
    # 检查nacos-sdk-python
    nacos_ok = check_nacos_sdk()
    
    # 检查其他依赖
    deps_ok = check_other_dependencies()
    
    # 测试Nacos客户端
    if nacos_ok:
        client_ok = test_nacos_connection()
    else:
        client_ok = False
    
    # 总结
    print("\n" + "="*50)
    if nacos_ok and deps_ok and client_ok:
        print("🎉 所有依赖检查通过！可以使用Nacos配置管理功能")
        print("\n下一步:")
        print("1. 启动Nacos服务器")
        print("2. 运行配置初始化脚本")
        print("3. 测试配置管理功能")
    else:
        print("❌ 依赖检查未通过，请解决上述问题")
        print("\n安装命令:")
        print("pip install nacos-sdk-python pyyaml requests")
    
    return 0 if (nacos_ok and deps_ok) else 1

if __name__ == "__main__":
    sys.exit(main())
