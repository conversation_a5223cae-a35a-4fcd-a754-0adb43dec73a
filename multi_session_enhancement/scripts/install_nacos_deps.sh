#!/bin/bash
# Nacos依赖一键安装脚本

set -e

echo "🚀 开始安装Nacos配置管理依赖..."

# 检查Python版本
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
echo "Python版本: $python_version"

if [[ $(echo "$python_version < 3.7" | bc -l) -eq 1 ]]; then
    echo "⚠️  警告: 建议使用Python 3.7+"
fi

# 检查pip是否可用
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 未找到，请先安装pip"
    exit 1
fi

echo "📦 安装nacos-sdk-python..."
pip3 install nacos-sdk-python>=0.1.6

echo "📦 安装其他依赖..."
pip3 install pyyaml>=6.0 requests>=2.25.0

echo "🔍 验证安装..."
python3 -c "
import nacos
import yaml
import requests
print('✅ 所有依赖安装成功!')
print(f'nacos-sdk-python版本: {getattr(nacos, \"__version__\", \"未知\")}')
"

echo "🎉 Nacos依赖安装完成!"
echo ""
echo "下一步:"
echo "1. 启动Nacos服务器: docker run --name nacos -e MODE=standalone -p 8848:8848 -d nacos/nacos-server"
echo "2. 运行依赖检查: python3 multi_session_enhancement/scripts/check_nacos_dependency.py"
echo "3. 初始化配置: python3 multi_session_enhancement/scripts/setup_nacos_configs.py"
