"""
自定义动作管理器 - 扩展数字人动作类型
支持多种动作类型：手势、表情、姿态、特殊动作等
"""
import json
import os
import logging
import glob
import cv2
import soundfile as sf
import numpy as np
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import shutil
from pathlib import Path

logger = logging.getLogger(__name__)

class ActionType(Enum):
    """动作类型枚举"""
    IDLE = "idle"                    # 静止/待机
    GREETING = "greeting"            # 打招呼
    NODDING = "nodding"             # 点头
    SHAKING_HEAD = "shaking_head"   # 摇头
    WAVING = "waving"               # 挥手
    POINTING = "pointing"           # 指向
    THUMBS_UP = "thumbs_up"         # 点赞
    THINKING = "thinking"           # 思考
    EXPLAINING = "explaining"       # 解释说明
    SURPRISED = "surprised"         # 惊讶
    HAPPY = "happy"                 # 开心
    SAD = "sad"                     # 悲伤
    ANGRY = "angry"                 # 生气
    CONFUSED = "confused"           # 困惑
    APPLAUDING = "applauding"       # 鼓掌
    PRESENTING = "presenting"       # 展示
    CUSTOM = "custom"               # 自定义

@dataclass
class ActionConfig:
    """动作配置类"""
    audiotype: int                  # 动作类型ID (数字)
    action_name: str               # 动作名称
    action_type: ActionType        # 动作类型
    imgpath: str                   # 图片序列路径
    audiopath: str                 # 音频文件路径
    description: str = ""          # 动作描述
    duration: float = 0.0          # 动作时长(秒)
    loop: bool = True              # 是否循环播放
    trigger_keywords: List[str] = None  # 触发关键词
    emotion_score: float = 0.0     # 情感分数 (-1到1)
    gesture_intensity: float = 1.0  # 手势强度 (0到1)
    enabled: bool = True           # 是否启用
    
    def __post_init__(self):
        if self.trigger_keywords is None:
            self.trigger_keywords = []

@dataclass
class ActionLibrary:
    """动作库"""
    actions: Dict[int, ActionConfig]
    version: str = "1.0"
    created_by: str = "LiveTalking"
    description: str = "数字人动作库"
    
    def __post_init__(self):
        if not isinstance(self.actions, dict):
            self.actions = {}

class CustomActionManager:
    """自定义动作管理器"""
    
    def __init__(self, config_path: str = "data/custom_config.json", 
                 actions_dir: str = "data/customvideo"):
        self.config_path = config_path
        self.actions_dir = actions_dir
        self.action_library = ActionLibrary(actions={})
        
        # 确保目录存在
        os.makedirs(actions_dir, exist_ok=True)
        
        # 加载现有配置
        self.load_config()
    
    def load_config(self) -> bool:
        """加载动作配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 兼容旧格式
                if isinstance(data, list):
                    self._convert_legacy_format(data)
                else:
                    self._load_new_format(data)
                
                logger.info(f"加载动作配置成功: {len(self.action_library.actions)} 个动作")
                return True
            else:
                # 创建默认配置
                self._create_default_config()
                return True
                
        except Exception as e:
            logger.error(f"加载动作配置失败: {e}")
            return False
    
    def _convert_legacy_format(self, legacy_data: List[Dict]):
        """转换旧格式配置"""
        logger.info("检测到旧格式配置，正在转换...")
        
        for i, item in enumerate(legacy_data):
            action_config = ActionConfig(
                audiotype=item.get('audiotype', i + 2),
                action_name=f"自定义动作{i + 1}",
                action_type=ActionType.CUSTOM,
                imgpath=item.get('imgpath', ''),
                audiopath=item.get('audiopath', ''),
                description=f"从旧配置转换的动作 {i + 1}"
            )
            self.action_library.actions[action_config.audiotype] = action_config
    
    def _load_new_format(self, data: Dict):
        """加载新格式配置"""
        if 'actions' in data:
            for audiotype_str, action_data in data['actions'].items():
                audiotype = int(audiotype_str)
                
                # 处理action_type
                action_type = action_data.get('action_type', 'custom')
                if isinstance(action_type, str):
                    try:
                        action_type = ActionType(action_type)
                    except ValueError:
                        action_type = ActionType.CUSTOM
                
                action_config = ActionConfig(
                    audiotype=audiotype,
                    action_name=action_data.get('action_name', f'动作{audiotype}'),
                    action_type=action_type,
                    imgpath=action_data.get('imgpath', ''),
                    audiopath=action_data.get('audiopath', ''),
                    description=action_data.get('description', ''),
                    duration=action_data.get('duration', 0.0),
                    loop=action_data.get('loop', True),
                    trigger_keywords=action_data.get('trigger_keywords', []),
                    emotion_score=action_data.get('emotion_score', 0.0),
                    gesture_intensity=action_data.get('gesture_intensity', 1.0),
                    enabled=action_data.get('enabled', True)
                )
                self.action_library.actions[audiotype] = action_config
        
        # 加载库信息
        self.action_library.version = data.get('version', '1.0')
        self.action_library.created_by = data.get('created_by', 'LiveTalking')
        self.action_library.description = data.get('description', '数字人动作库')
    
    def _create_default_config(self):
        """创建默认配置"""
        logger.info("创建默认动作配置...")
        
        # 添加一些默认动作
        default_actions = [
            ActionConfig(
                audiotype=2,
                action_name="打招呼",
                action_type=ActionType.GREETING,
                imgpath="data/customvideo/greeting/images",
                audiopath="data/customvideo/greeting/audio.wav",
                description="友好的打招呼手势",
                trigger_keywords=["你好", "hello", "hi", "打招呼"],
                emotion_score=0.8
            ),
            ActionConfig(
                audiotype=3,
                action_name="点头同意",
                action_type=ActionType.NODDING,
                imgpath="data/customvideo/nodding/images",
                audiopath="data/customvideo/nodding/audio.wav",
                description="表示同意的点头动作",
                trigger_keywords=["是的", "对", "同意", "没错"],
                emotion_score=0.5
            ),
            ActionConfig(
                audiotype=4,
                action_name="摇头否定",
                action_type=ActionType.SHAKING_HEAD,
                imgpath="data/customvideo/shaking_head/images",
                audiopath="data/customvideo/shaking_head/audio.wav",
                description="表示否定的摇头动作",
                trigger_keywords=["不", "不是", "错了", "不对"],
                emotion_score=-0.3
            ),
            ActionConfig(
                audiotype=5,
                action_name="点赞",
                action_type=ActionType.THUMBS_UP,
                imgpath="data/customvideo/thumbs_up/images",
                audiopath="data/customvideo/thumbs_up/audio.wav",
                description="表示赞赏的竖拇指动作",
                trigger_keywords=["棒", "好", "赞", "厉害", "excellent"],
                emotion_score=0.9
            ),
            ActionConfig(
                audiotype=6,
                action_name="思考",
                action_type=ActionType.THINKING,
                imgpath="data/customvideo/thinking/images",
                audiopath="data/customvideo/thinking/audio.wav",
                description="思考时的手势动作",
                trigger_keywords=["让我想想", "思考", "考虑", "想一下"],
                emotion_score=0.0
            )
        ]
        
        for action in default_actions:
            self.action_library.actions[action.audiotype] = action
        
        self.save_config()
    
    def save_config(self) -> bool:
        """保存动作配置"""
        try:
            # 转换为可序列化的格式
            config_data = {
                "version": self.action_library.version,
                "created_by": self.action_library.created_by,
                "description": self.action_library.description,
                "actions": {}
            }
            
            for audiotype, action in self.action_library.actions.items():
                action_dict = asdict(action)
                # 转换枚举为字符串
                action_dict['action_type'] = action.action_type.value
                config_data["actions"][str(audiotype)] = action_dict
            
            # 保存配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"保存动作配置成功: {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存动作配置失败: {e}")
            return False
    
    def add_action(self, action_config: ActionConfig) -> bool:
        """添加新动作"""
        try:
            # 检查audiotype是否已存在
            if action_config.audiotype in self.action_library.actions:
                logger.warning(f"动作类型 {action_config.audiotype} 已存在，将覆盖")
            
            # 验证路径
            if not self._validate_action_paths(action_config):
                return False
            
            # 添加到库中
            self.action_library.actions[action_config.audiotype] = action_config
            
            # 保存配置
            if self.save_config():
                logger.info(f"添加动作成功: {action_config.action_name}")
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"添加动作失败: {e}")
            return False
    
    def remove_action(self, audiotype: int) -> bool:
        """删除动作"""
        try:
            if audiotype not in self.action_library.actions:
                logger.warning(f"动作类型 {audiotype} 不存在")
                return False
            
            action_name = self.action_library.actions[audiotype].action_name
            del self.action_library.actions[audiotype]
            
            if self.save_config():
                logger.info(f"删除动作成功: {action_name}")
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"删除动作失败: {e}")
            return False
    
    def get_action(self, audiotype: int) -> Optional[ActionConfig]:
        """获取动作配置"""
        return self.action_library.actions.get(audiotype)
    
    def list_actions(self) -> Dict[int, ActionConfig]:
        """列出所有动作"""
        return self.action_library.actions.copy()
    
    def find_actions_by_keyword(self, keyword: str) -> List[ActionConfig]:
        """根据关键词查找动作"""
        matching_actions = []
        keyword_lower = keyword.lower()
        
        for action in self.action_library.actions.values():
            # 检查触发关键词
            for trigger in action.trigger_keywords:
                if keyword_lower in trigger.lower():
                    matching_actions.append(action)
                    break
            else:
                # 检查动作名称和描述
                if (keyword_lower in action.action_name.lower() or 
                    keyword_lower in action.description.lower()):
                    matching_actions.append(action)
        
        return matching_actions
    
    def find_actions_by_type(self, action_type: ActionType) -> List[ActionConfig]:
        """根据动作类型查找动作"""
        return [action for action in self.action_library.actions.values() 
                if action.action_type == action_type]
    
    def find_actions_by_emotion(self, min_score: float = 0.0, 
                               max_score: float = 1.0) -> List[ActionConfig]:
        """根据情感分数查找动作"""
        return [action for action in self.action_library.actions.values() 
                if min_score <= action.emotion_score <= max_score]
    
    def _validate_action_paths(self, action_config: ActionConfig) -> bool:
        """验证动作路径"""
        # 检查图片路径
        if action_config.imgpath and not os.path.exists(action_config.imgpath):
            logger.warning(f"图片路径不存在: {action_config.imgpath}")
        
        # 检查音频路径
        if action_config.audiopath and not os.path.exists(action_config.audiopath):
            logger.warning(f"音频路径不存在: {action_config.audiopath}")
        
        return True
    
    def create_action_from_video(self, video_path: str, action_name: str, 
                                action_type: ActionType, audiotype: int,
                                trigger_keywords: List[str] = None) -> bool:
        """从视频文件创建动作"""
        try:
            # 创建动作目录
            action_dir = os.path.join(self.actions_dir, action_name.lower().replace(' ', '_'))
            img_dir = os.path.join(action_dir, 'images')
            os.makedirs(img_dir, exist_ok=True)
            
            # 提取视频帧
            cap = cv2.VideoCapture(video_path)
            frame_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 保存帧
                frame_path = os.path.join(img_dir, f"{frame_count:08d}.png")
                cv2.imwrite(frame_path, frame)
                frame_count += 1
            
            cap.release()
            
            # 提取音频 (如果需要)
            audio_path = os.path.join(action_dir, 'audio.wav')
            # 这里可以使用ffmpeg提取音频，暂时创建空音频
            sample_rate = 16000
            duration = frame_count / 25.0  # 假设25fps
            silence = np.zeros(int(sample_rate * duration), dtype=np.float32)
            sf.write(audio_path, silence, sample_rate)
            
            # 创建动作配置
            action_config = ActionConfig(
                audiotype=audiotype,
                action_name=action_name,
                action_type=action_type,
                imgpath=img_dir,
                audiopath=audio_path,
                description=f"从视频 {os.path.basename(video_path)} 创建的动作",
                duration=duration,
                trigger_keywords=trigger_keywords or []
            )
            
            return self.add_action(action_config)
            
        except Exception as e:
            logger.error(f"从视频创建动作失败: {e}")
            return False
    
    def export_legacy_format(self) -> List[Dict]:
        """导出为旧格式 (兼容原系统)"""
        legacy_format = []
        
        for action in self.action_library.actions.values():
            legacy_format.append({
                "audiotype": action.audiotype,
                "imgpath": action.imgpath,
                "audiopath": action.audiopath
            })
        
        return legacy_format
    
    def get_next_available_audiotype(self) -> int:
        """获取下一个可用的audiotype"""
        if not self.action_library.actions:
            return 2  # 从2开始，0和1通常保留
        
        return max(self.action_library.actions.keys()) + 1
    
    def validate_all_actions(self) -> Dict[int, List[str]]:
        """验证所有动作的完整性"""
        validation_results = {}
        
        for audiotype, action in self.action_library.actions.items():
            issues = []
            
            # 检查图片路径
            if not action.imgpath:
                issues.append("缺少图片路径")
            elif not os.path.exists(action.imgpath):
                issues.append(f"图片路径不存在: {action.imgpath}")
            else:
                # 检查图片文件
                img_files = glob.glob(os.path.join(action.imgpath, '*.[jpJP][pnPN]*[gG]'))
                if not img_files:
                    issues.append("图片目录为空")
            
            # 检查音频路径
            if not action.audiopath:
                issues.append("缺少音频路径")
            elif not os.path.exists(action.audiopath):
                issues.append(f"音频路径不存在: {action.audiopath}")
            
            if issues:
                validation_results[audiotype] = issues
        
        return validation_results

# 全局动作管理器实例
_global_action_manager: Optional[CustomActionManager] = None

def init_action_manager(config_path: str = "data/custom_config.json",
                       actions_dir: str = "data/customvideo") -> CustomActionManager:
    """初始化全局动作管理器"""
    global _global_action_manager
    _global_action_manager = CustomActionManager(config_path, actions_dir)
    return _global_action_manager

def get_action_manager() -> Optional[CustomActionManager]:
    """获取全局动作管理器"""
    return _global_action_manager
