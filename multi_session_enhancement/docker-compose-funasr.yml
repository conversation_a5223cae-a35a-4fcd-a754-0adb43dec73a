version: '3.8'

services:
  # FunASR服务
  funasr-server:
    build:
      context: .
      dockerfile: docker/Dockerfile.funasr
    container_name: funasr-asr-server
    ports:
      - "10096:10096"
    volumes:
      - ./models:/app/models
      - ./cache:/app/cache
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - HF_HOME=/app/cache
      - TRANSFORMERS_CACHE=/app/cache
      - FUNASR_CACHE_DIR=/app/cache
    command: >
      python funasr_server.py
      --model iic/SenseVoiceSmall
      --device cuda
      --host 0.0.0.0
      --port 10096
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:10096/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: funasr-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - funasr-server
    restart: unless-stopped

networks:
  default:
    name: funasr-network

volumes:
  models:
    driver: local
  cache:
    driver: local
