"""
智能打断管理器 - 支持语音指令打断数字人说话
包括关键词检测、语音活动检测、智能打断策略
"""
import asyncio
import logging
import time
import re
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
import threading
from queue import Queue, Empty

logger = logging.getLogger(__name__)

class InterruptType(Enum):
    """打断类型"""
    KEYWORD = "keyword"          # 关键词打断
    VOICE_ACTIVITY = "voice"     # 语音活动打断
    MANUAL = "manual"            # 手动打断
    TIMEOUT = "timeout"          # 超时打断

@dataclass
class InterruptConfig:
    """打断配置"""
    # 关键词打断
    interrupt_keywords: List[str] = None
    keyword_sensitivity: float = 0.8  # 关键词匹配敏感度
    
    # 语音活动打断
    voice_activity_threshold: float = 0.5  # 语音活动阈值
    voice_activity_duration: float = 1.0   # 持续时间(秒)
    
    # 超时打断
    max_speaking_duration: float = 30.0    # 最大说话时长(秒)
    
    # 打断策略
    interrupt_delay: float = 0.5           # 打断延迟(秒)
    resume_after_interrupt: bool = False   # 打断后是否恢复
    
    # 智能打断
    smart_interrupt: bool = True           # 启用智能打断
    sentence_boundary_detection: bool = True  # 句子边界检测
    
    def __post_init__(self):
        if self.interrupt_keywords is None:
            self.interrupt_keywords = [
                "停", "停止", "别说了", "够了", "打住", "暂停",
                "stop", "pause", "enough", "shut up",
                "等等", "等一下", "慢着", "hold on"
            ]

@dataclass
class InterruptEvent:
    """打断事件"""
    session_id: str
    interrupt_type: InterruptType
    trigger_text: Optional[str] = None
    confidence: float = 1.0
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class VoiceActivityDetector:
    """语音活动检测器"""
    
    def __init__(self, config: InterruptConfig):
        self.config = config
        self.activity_buffer = []
        self.last_activity_time = 0
    
    def detect_activity(self, audio_chunk: bytes, volume_level: float = None) -> bool:
        """检测语音活动"""
        current_time = time.time()
        
        # 简单的音量检测 (实际应用中可以使用更复杂的VAD算法)
        if volume_level is None:
            # 计算音频能量作为音量指标
            import numpy as np
            audio_array = np.frombuffer(audio_chunk, dtype=np.int16)
            volume_level = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))
            volume_level = volume_level / 32768.0  # 归一化到0-1
        
        # 检测是否超过阈值
        is_active = volume_level > self.config.voice_activity_threshold
        
        if is_active:
            self.last_activity_time = current_time
            self.activity_buffer.append(current_time)
        
        # 清理旧的活动记录
        cutoff_time = current_time - self.config.voice_activity_duration
        self.activity_buffer = [t for t in self.activity_buffer if t > cutoff_time]
        
        # 判断是否满足持续活动条件
        if len(self.activity_buffer) > 0:
            duration = current_time - min(self.activity_buffer)
            return duration >= self.config.voice_activity_duration
        
        return False

class KeywordDetector:
    """关键词检测器"""
    
    def __init__(self, config: InterruptConfig):
        self.config = config
        self.keywords = [kw.lower() for kw in config.interrupt_keywords]
    
    def detect_keywords(self, text: str) -> Optional[str]:
        """检测打断关键词"""
        text_lower = text.lower().strip()
        
        for keyword in self.keywords:
            if keyword in text_lower:
                # 计算匹配度
                confidence = self._calculate_confidence(text_lower, keyword)
                if confidence >= self.config.keyword_sensitivity:
                    return keyword
        
        return None
    
    def _calculate_confidence(self, text: str, keyword: str) -> float:
        """计算关键词匹配置信度"""
        # 简单的匹配度计算
        if text == keyword:
            return 1.0
        elif text.startswith(keyword) or text.endswith(keyword):
            return 0.9
        elif keyword in text:
            return 0.8
        else:
            return 0.0

class SentenceBoundaryDetector:
    """句子边界检测器"""
    
    def __init__(self):
        self.sentence_endings = ['。', '！', '？', '.', '!', '?', '；', ';']
        self.pause_indicators = ['，', ',', '、']
    
    def is_sentence_boundary(self, text: str, position: int = -1) -> bool:
        """检测是否在句子边界"""
        if position == -1:
            position = len(text) - 1
        
        if position < 0 or position >= len(text):
            return False
        
        char = text[position]
        return char in self.sentence_endings
    
    def find_next_boundary(self, text: str, start_pos: int = 0) -> int:
        """找到下一个句子边界"""
        for i in range(start_pos, len(text)):
            if text[i] in self.sentence_endings:
                return i
        return len(text)

class InterruptManager:
    """智能打断管理器"""
    
    def __init__(self, config: InterruptConfig = None):
        self.config = config or InterruptConfig()
        self.voice_detector = VoiceActivityDetector(self.config)
        self.keyword_detector = KeywordDetector(self.config)
        self.sentence_detector = SentenceBoundaryDetector()
        
        self.active_sessions: Dict[str, Dict] = {}
        self.interrupt_callbacks: Dict[str, List[Callable]] = {}
        self.event_queue = Queue()
        
        self._running = False
        self._worker_thread = None
    
    def start(self):
        """启动打断管理器"""
        if not self._running:
            self._running = True
            self._worker_thread = threading.Thread(target=self._process_events)
            self._worker_thread.start()
            logger.info("打断管理器已启动")
    
    def stop(self):
        """停止打断管理器"""
        self._running = False
        if self._worker_thread:
            self._worker_thread.join()
        logger.info("打断管理器已停止")
    
    def register_session(self, session_id: str, nerfreal_instance):
        """注册会话"""
        self.active_sessions[session_id] = {
            'nerfreal': nerfreal_instance,
            'speaking_start_time': None,
            'last_text': '',
            'interrupt_count': 0
        }
        self.interrupt_callbacks[session_id] = []
        logger.info(f"注册会话: {session_id}")
    
    def unregister_session(self, session_id: str):
        """注销会话"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
        if session_id in self.interrupt_callbacks:
            del self.interrupt_callbacks[session_id]
        logger.info(f"注销会话: {session_id}")
    
    def add_interrupt_callback(self, session_id: str, callback: Callable):
        """添加打断回调函数"""
        if session_id not in self.interrupt_callbacks:
            self.interrupt_callbacks[session_id] = []
        self.interrupt_callbacks[session_id].append(callback)
    
    def process_audio_input(self, session_id: str, audio_chunk: bytes, volume_level: float = None):
        """处理音频输入 - 检测语音活动打断"""
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        
        # 只在数字人说话时检测打断
        if not session['nerfreal'].is_speaking():
            return
        
        # 检测语音活动
        if self.voice_detector.detect_activity(audio_chunk, volume_level):
            event = InterruptEvent(
                session_id=session_id,
                interrupt_type=InterruptType.VOICE_ACTIVITY,
                confidence=0.8
            )
            self.event_queue.put(event)
    
    def process_text_input(self, session_id: str, text: str):
        """处理文本输入 - 检测关键词打断"""
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        
        # 检测打断关键词
        keyword = self.keyword_detector.detect_keywords(text)
        if keyword:
            event = InterruptEvent(
                session_id=session_id,
                interrupt_type=InterruptType.KEYWORD,
                trigger_text=keyword,
                confidence=0.9
            )
            self.event_queue.put(event)
            return True  # 表示检测到打断指令
        
        return False
    
    def manual_interrupt(self, session_id: str):
        """手动打断"""
        event = InterruptEvent(
            session_id=session_id,
            interrupt_type=InterruptType.MANUAL,
            confidence=1.0
        )
        self.event_queue.put(event)
    
    def check_timeout_interrupt(self, session_id: str):
        """检查超时打断"""
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        
        if session['speaking_start_time'] and session['nerfreal'].is_speaking():
            speaking_duration = time.time() - session['speaking_start_time']
            if speaking_duration > self.config.max_speaking_duration:
                event = InterruptEvent(
                    session_id=session_id,
                    interrupt_type=InterruptType.TIMEOUT,
                    confidence=1.0
                )
                self.event_queue.put(event)
    
    def on_speaking_start(self, session_id: str):
        """数字人开始说话时调用"""
        if session_id in self.active_sessions:
            self.active_sessions[session_id]['speaking_start_time'] = time.time()
    
    def on_speaking_end(self, session_id: str):
        """数字人停止说话时调用"""
        if session_id in self.active_sessions:
            self.active_sessions[session_id]['speaking_start_time'] = None
    
    def _process_events(self):
        """处理打断事件的工作线程"""
        while self._running:
            try:
                event = self.event_queue.get(timeout=1.0)
                self._handle_interrupt_event(event)
            except Empty:
                # 检查超时打断
                for session_id in list(self.active_sessions.keys()):
                    self.check_timeout_interrupt(session_id)
                continue
            except Exception as e:
                logger.error(f"处理打断事件失败: {e}")
    
    def _handle_interrupt_event(self, event: InterruptEvent):
        """处理打断事件"""
        session_id = event.session_id
        
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        nerfreal = session['nerfreal']
        
        # 检查是否正在说话
        if not nerfreal.is_speaking():
            return
        
        logger.info(f"处理打断事件: {session_id} - {event.interrupt_type.value}")
        
        # 智能打断策略
        if self.config.smart_interrupt and event.interrupt_type != InterruptType.MANUAL:
            if not self._should_interrupt(session_id, event):
                return
        
        # 执行打断
        self._execute_interrupt(session_id, event)
        
        # 调用回调函数
        for callback in self.interrupt_callbacks.get(session_id, []):
            try:
                callback(event)
            except Exception as e:
                logger.error(f"打断回调函数执行失败: {e}")
    
    def _should_interrupt(self, session_id: str, event: InterruptEvent) -> bool:
        """智能判断是否应该打断"""
        session = self.active_sessions[session_id]
        
        # 频繁打断保护
        if session['interrupt_count'] > 3:
            time_since_last = time.time() - (session.get('last_interrupt_time', 0))
            if time_since_last < 5.0:  # 5秒内不允许频繁打断
                return False
        
        # 句子边界检测
        if self.config.sentence_boundary_detection:
            current_text = session.get('last_text', '')
            if current_text and not self.sentence_detector.is_sentence_boundary(current_text):
                # 等待句子结束
                return False
        
        return True
    
    def _execute_interrupt(self, session_id: str, event: InterruptEvent):
        """执行打断操作"""
        session = self.active_sessions[session_id]
        nerfreal = session['nerfreal']
        
        # 添加延迟以确保自然的打断
        if self.config.interrupt_delay > 0:
            time.sleep(self.config.interrupt_delay)
        
        # 执行打断
        nerfreal.flush_talk()
        
        # 更新统计信息
        session['interrupt_count'] += 1
        session['last_interrupt_time'] = time.time()
        
        logger.info(f"成功打断会话: {session_id} - 类型: {event.interrupt_type.value}")

# 全局打断管理器实例
_global_interrupt_manager: Optional[InterruptManager] = None

def init_interrupt_manager(config: InterruptConfig = None) -> InterruptManager:
    """初始化全局打断管理器"""
    global _global_interrupt_manager
    _global_interrupt_manager = InterruptManager(config)
    _global_interrupt_manager.start()
    return _global_interrupt_manager

def get_interrupt_manager() -> Optional[InterruptManager]:
    """获取全局打断管理器"""
    return _global_interrupt_manager

def shutdown_interrupt_manager():
    """关闭全局打断管理器"""
    global _global_interrupt_manager
    if _global_interrupt_manager:
        _global_interrupt_manager.stop()
        _global_interrupt_manager = None
