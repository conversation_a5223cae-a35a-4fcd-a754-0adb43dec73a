"""
增强版应用入口 - 集成多会话形象声音配置功能
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入原始app模块的所有内容
from app import *
from multi_session_enhancement.api_extensions import APIExtensions
from multi_session_enhancement.session_manager import session_manager
from multi_session_enhancement.resource_manager import resource_manager
from multi_session_enhancement.nacos_config_manager import init_config_manager, get_config_manager

def enhanced_build_nerfreal(sessionid: int, session_config=None) -> BaseReal:
    """增强版的build_nerfreal函数，支持会话级配置"""
    if session_config is None:
        # 使用原始逻辑
        return build_nerfreal(sessionid)
    
    # 使用会话配置创建实例
    temp_opt = type('TempOpt', (), {})()
    
    # 复制全局配置
    for attr in dir(opt):
        if not attr.startswith('_'):
            setattr(temp_opt, attr, getattr(opt, attr))
    
    # 应用会话配置
    temp_opt.sessionid = sessionid
    temp_opt.model = session_config.model
    temp_opt.avatar_id = session_config.avatar_id
    temp_opt.tts = session_config.tts
    temp_opt.REF_FILE = session_config.voice
    temp_opt.REF_TEXT = session_config.ref_text
    temp_opt.TTS_SERVER = session_config.tts_server
    temp_opt.batch_size = session_config.batch_size
    temp_opt.fps = session_config.fps
    temp_opt.W = session_config.W
    temp_opt.H = session_config.H
    temp_opt.customvideo_config = session_config.customvideo_config
    
    # 从资源管理器加载资源
    model_resource = resource_manager.load_model(session_config.model)
    avatar_resource = resource_manager.load_avatar(session_config.avatar_id)
    
    # 如果资源加载失败，回退到全局资源
    if model_resource is None:
        model_resource = model
    if avatar_resource is None:
        avatar_resource = avatar
    
    # 创建实例
    if temp_opt.model == 'wav2lip':
        from lipreal import LipReal
        nerfreal = LipReal(temp_opt, model_resource, avatar_resource)
    elif temp_opt.model == 'musetalk':
        from musereal import MuseReal
        nerfreal = MuseReal(temp_opt, model_resource, avatar_resource)
    elif temp_opt.model == 'ultralight':
        from lightreal import LightReal
        nerfreal = LightReal(temp_opt, model_resource, avatar_resource)
    else:
        raise ValueError(f"Unknown model type: {temp_opt.model}")
    
    return nerfreal

async def enhanced_offer(request):
    """增强版的offer函数，保持向后兼容"""
    params = await request.json()
    
    # 检查是否包含会话配置
    if 'session_config' in params:
        # 使用新的API扩展处理
        api_ext = APIExtensions(None, enhanced_build_nerfreal, nerfreals)
        return await api_ext.offer_with_config(request)
    
    # 使用原始逻辑处理
    offer = RTCSessionDescription(sdp=params["sdp"], type=params["type"])

    if len(nerfreals) >= opt.max_session:
        logger.info('reach max session')
        return web.Response(
            content_type="application/json",
            text=json.dumps(
                {"code": -1, "msg": "reach max session"}
            ),
        )
    
    sessionid = randN(6)
    logger.info('sessionid=%d', sessionid)
    nerfreals[sessionid] = None
    
    # 创建默认会话配置
    session_config = session_manager.create_session(sessionid)
    nerfreal = await asyncio.get_event_loop().run_in_executor(
        None, enhanced_build_nerfreal, sessionid, session_config
    )
    nerfreals[sessionid] = nerfreal
    session_manager.set_session_instance(sessionid, nerfreal)
    
    # 其余逻辑与原始offer函数相同
    ice_server = RTCIceServer(urls='stun:stun.miwifi.com:3478')
    pc = RTCPeerConnection(configuration=RTCConfiguration(iceServers=[ice_server]))
    pcs.add(pc)

    @pc.on("connectionstatechange")
    async def on_connectionstatechange():
        logger.info("Connection state is %s" % pc.connectionState)
        if pc.connectionState == "failed":
            await pc.close()
            pcs.discard(pc)
            del nerfreals[sessionid]
            session_manager.remove_session(sessionid)
            session_manager.remove_session_instance(sessionid)
        if pc.connectionState == "closed":
            pcs.discard(pc)
            del nerfreals[sessionid]
            session_manager.remove_session(sessionid)
            session_manager.remove_session_instance(sessionid)
            gc.collect()

    player = HumanPlayer(nerfreals[sessionid])
    audio_sender = pc.addTrack(player.audio)
    video_sender = pc.addTrack(player.video)
    capabilities = RTCRtpSender.getCapabilities("video")
    preferences = list(filter(lambda x: x.name == "H264", capabilities.codecs))
    preferences += list(filter(lambda x: x.name == "VP8", capabilities.codecs))
    preferences += list(filter(lambda x: x.name == "rtx", capabilities.codecs))
    transceiver = pc.getTransceivers()[1]
    transceiver.setCodecPreferences(preferences)

    await pc.setRemoteDescription(offer)

    answer = await pc.createAnswer()
    await pc.setLocalDescription(answer)

    return web.Response(
        content_type="application/json",
        text=json.dumps(
            {
                "sdp": pc.localDescription.sdp, 
                "type": pc.localDescription.type,
                "session_id": sessionid
            }
        ),
    )

def setup_enhanced_app():
    """设置增强版应用"""
    global appasync

    # 初始化Nacos配置管理器
    nacos_client = get_nacos_client()
    if nacos_client:
        config_manager = init_config_manager(nacos_client)
        logger.info("Nacos配置管理器初始化成功")
    else:
        logger.warning("Nacos客户端未初始化，配置管理器将降级到环境变量")
        config_manager = init_config_manager()

    # 替换原始的offer路由
    # 移除原始路由
    for route in list(appasync.router.routes()):
        if hasattr(route, '_path') and route._path == '/offer':
            appasync.router._resources.remove(route._resource)

    # 添加增强版路由
    appasync.router.add_post("/offer", enhanced_offer)

    # 添加API扩展
    api_extensions = APIExtensions(appasync, enhanced_build_nerfreal, nerfreals)

    # 设置默认配置
    default_config = {
        'avatar_id': opt.avatar_id,
        'model': opt.model,
        'tts': opt.tts,
        'voice': opt.REF_FILE,
        'ref_text': opt.REF_TEXT,
        'tts_server': opt.TTS_SERVER,
        'batch_size': opt.batch_size,
        'fps': opt.fps,
        'W': opt.W,
        'H': opt.H,
        'customvideo_config': opt.customvideo_config
    }
    session_manager.set_default_config(default_config)

    logger.info("Enhanced multi-session support enabled")
    logger.info("Nacos configuration management enabled")

if __name__ == '__main__':
    # 在应用启动前设置增强功能
    setup_enhanced_app()
    
    # 启动应用（使用原始的启动逻辑）
    logger.info(f"Enhanced LiveTalking server starting on port {opt.listenport}")
    logger.info(f"Multi-session support: enabled")
    logger.info(f"Max sessions: {opt.max_session}")
    
    web.run_app(appasync, host='0.0.0.0', port=opt.listenport)
