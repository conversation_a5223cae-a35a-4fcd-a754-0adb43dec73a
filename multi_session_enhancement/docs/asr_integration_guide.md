# ASR集成指南 - 支持多种语音识别服务

## 📋 概述

LiveTalking现在支持多种ASR (自动语音识别) 服务，包括：

- **FunASR** - 阿里巴巴开源，WebSocket实时流式识别
- **OpenAI Whisper API** - OpenAI官方API，高准确度
- **SenseVoice** - 阿里开源模型，可自部署
- **Azure Speech** - 微软云服务
- **腾讯云ASR** - 腾讯云语音识别
- **百度ASR** - 百度云语音识别

## 🚀 快速开始

### 1. 配置ASR服务

创建 `asr_config.json` 配置文件：

```json
{
  "provider": "sensevoice",
  "enabled": true,
  "language": "zh",
  "sample_rate": 16000,
  "format": "wav",
  "sensevoice_api_url": "http://your-server:8000",
  "sensevoice_model": "sensevoice-small",
  "sensevoice_language": "auto",
  "sensevoice_use_itn": true
}
```

### 2. 初始化ASR系统

```python
from multi_session_enhancement.asr_config_manager import init_asr_config_manager
from multi_session_enhancement.enhanced_asr_handler import init_enhanced_asr

# 初始化ASR配置管理器
asr_config_manager = init_asr_config_manager("asr_config.json")

# 在aiohttp应用中集成
await init_enhanced_asr(app, "asr_config.json")
```

### 3. 使用ASR服务

```python
# 转录音频文件
text = await asr_config_manager.transcribe_audio(audio_data, "wav")
print(f"识别结果: {text}")
```

## 🔧 配置详解

### SenseVoice配置

```json
{
  "provider": "sensevoice",
  "enabled": true,
  "sensevoice_api_url": "http://localhost:8000",
  "sensevoice_model": "sensevoice-small",
  "sensevoice_language": "auto",
  "sensevoice_use_itn": true
}
```

**部署SenseVoice服务:**

```bash
# Docker部署
docker run -d -p 8000:8000 --name sensevoice-server sensevoice:latest

# 测试服务
curl -X POST http://localhost:8000/v1/audio/transcriptions \
  -F "audio=@test.wav" \
  -F "model=sensevoice-small"
```

### OpenAI Whisper API配置

```json
{
  "provider": "openai_whisper",
  "enabled": true,
  "openai_api_key": "your-openai-api-key",
  "openai_base_url": "https://api.openai.com/v1",
  "openai_model": "whisper-1",
  "openai_temperature": 0.0,
  "openai_language": "zh"
}
```

**自部署Whisper服务:**

```bash
# 使用Faster-Whisper
docker run -d -p 8001:8000 fedirz/faster-whisper-server:latest

# 配置为OpenAI兼容接口
{
  "provider": "openai_whisper",
  "openai_api_key": "dummy-key",
  "openai_base_url": "http://localhost:8001/v1",
  "openai_model": "large-v3"
}
```

### FunASR配置 (默认)

```json
{
  "provider": "funasr",
  "enabled": true,
  "funasr_websocket_url": "wss://www.funasr.com:10096/",
  "funasr_mode": "2pass",
  "funasr_use_itn": true
}
```

## 🏗️ 架构说明

### ASR处理流程

```
用户语音 → 音频采集 → ASR服务 → 文字结果 → LLM对话 → TTS合成 → 数字人播放
```

### 双重ASR系统

LiveTalking使用双重ASR系统：

1. **语音转文字ASR** (本指南内容)
   - 用途: 将用户语音转换为文字，用于对话
   - 技术: FunASR、Whisper、SenseVoice等
   - 可配置: ✅

2. **音频特征提取ASR** (固定)
   - 用途: 提取音频特征，用于数字人嘴型同步
   - 技术: LipASR、MuseASR、HubertASR
   - 可配置: ❌ (根据数字人模型自动选择)

## 📡 API接口

### WebSocket接口

```javascript
// 连接ASR WebSocket
const ws = new WebSocket('ws://localhost:8080/asr/ws?session_id=123');

// 发送音频数据
ws.send(audioBuffer);

// 接收识别结果
ws.onmessage = function(event) {
    const result = JSON.parse(event.data);
    console.log('识别结果:', result.text);
};
```

### REST API接口

```bash
# 文件转录
curl -X POST http://localhost:8080/asr/transcribe \
  -F "audio=@audio.wav"

# 获取配置
curl http://localhost:8080/asr/config

# 更新配置
curl -X POST http://localhost:8080/asr/config \
  -H "Content-Type: application/json" \
  -d '{"config": {"provider": "sensevoice"}}'

# 测试配置
curl -X POST http://localhost:8080/asr/test
```

## 🐳 Docker部署

### 完整部署方案

```yaml
version: '3.8'

services:
  # SenseVoice ASR服务
  sensevoice:
    image: sensevoice:latest
    ports:
      - "8000:8000"
    environment:
      - MODEL_NAME=sensevoice-small

  # Faster-Whisper服务
  faster-whisper:
    image: fedirz/faster-whisper-server:latest
    ports:
      - "8001:8000"
    environment:
      - MODEL=large-v3

  # LiveTalking主应用
  livetalking:
    build: .
    ports:
      - "8080:8080"
    environment:
      - ASR_PROVIDER=sensevoice
      - SENSEVOICE_API_URL=http://sensevoice:8000
    depends_on:
      - sensevoice
```

### 部署脚本

```bash
#!/bin/bash

# 1. 启动ASR服务
docker-compose up -d sensevoice faster-whisper

# 2. 等待服务就绪
sleep 30

# 3. 测试ASR服务
python examples/sensevoice_deployment_example.py

# 4. 启动LiveTalking
docker-compose up -d livetalking
```

## 🔄 配置切换

### 运行时切换ASR服务

```python
from multi_session_enhancement.asr_config_manager import get_asr_config_manager

# 获取配置管理器
asr_manager = get_asr_config_manager()

# 切换到SenseVoice
asr_manager.update_config(
    provider="sensevoice",
    sensevoice_api_url="http://localhost:8000"
)

# 切换到OpenAI Whisper
asr_manager.update_config(
    provider="openai_whisper",
    openai_api_key="your-api-key"
)
```

### 环境变量配置

```bash
# 设置ASR提供商
export ASR_PROVIDER=sensevoice
export SENSEVOICE_API_URL=http://localhost:8000

# 或使用OpenAI
export ASR_PROVIDER=openai_whisper
export OPENAI_API_KEY=your-api-key
export OPENAI_BASE_URL=https://api.openai.com/v1
```

## 🧪 测试和调试

### 测试ASR配置

```python
import asyncio
from multi_session_enhancement.examples.sensevoice_deployment_example import SenseVoiceDeploymentExample

async def test_asr():
    example = SenseVoiceDeploymentExample()
    
    # 创建配置
    config = example.create_sensevoice_config("http://localhost:8000")
    
    # 测试服务
    success = await example.test_asr_service(config)
    print(f"测试结果: {'成功' if success else '失败'}")

asyncio.run(test_asr())
```

### 调试日志

```python
import logging

# 启用详细日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger('asr_config_manager')
logger.setLevel(logging.DEBUG)
```

## 🚨 故障排除

### 常见问题

1. **SenseVoice连接失败**
   ```bash
   # 检查服务状态
   curl http://localhost:8000/health
   
   # 查看容器日志
   docker logs sensevoice-asr
   ```

2. **OpenAI API限制**
   ```python
   # 检查API配额和限制
   # 使用自部署Whisper服务作为替代
   ```

3. **音频格式不支持**
   ```python
   # 确保音频格式为16kHz WAV
   # 使用ffmpeg转换格式
   ffmpeg -i input.mp3 -ar 16000 -ac 1 output.wav
   ```

### 性能优化

1. **批量处理**: 累积音频数据批量发送
2. **缓存结果**: 缓存相同音频的识别结果
3. **负载均衡**: 部署多个ASR服务实例
4. **模型选择**: 根据需求选择合适的模型大小

## 📚 参考资料

- [FunASR官方文档](https://github.com/alibaba-damo-academy/FunASR)
- [OpenAI Whisper API文档](https://platform.openai.com/docs/guides/speech-to-text)
- [SenseVoice项目](https://github.com/FunAudioLLM/SenseVoice)
- [Faster-Whisper项目](https://github.com/guillaumekln/faster-whisper)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进ASR集成功能！
