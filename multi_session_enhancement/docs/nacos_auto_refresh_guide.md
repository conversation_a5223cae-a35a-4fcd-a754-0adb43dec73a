# Nacos配置自动刷新指南

## 🎯 概述

Nacos配置自动刷新功能允许LiveTalking应用在运行时自动检测和应用Nacos中的配置变更，无需重启服务。这大大提高了配置管理的灵活性和运维效率。

## ✨ 主要特性

### 1. **自动配置刷新**
- ✅ 定期检查Nacos配置变更（默认30秒）
- ✅ 自动更新本地配置缓存
- ✅ 支持可配置的刷新间隔

### 2. **配置变更监听**
- ✅ 实时监听配置变更事件
- ✅ 支持自定义变更处理逻辑
- ✅ 自动重新初始化相关服务

### 3. **智能缓存管理**
- ✅ 本地配置缓存提高性能
- ✅ 缓存过期自动刷新
- ✅ 支持手动清除缓存

### 4. **降级保护**
- ✅ Nacos不可用时自动降级到环境变量
- ✅ 配置解析失败时的容错处理
- ✅ 服务可用性保障

## 🚀 快速开始

### 1. 启用自动刷新

```python
from multi_session_enhancement.nacos_config_manager_v2 import init_config_manager_v2
from nacos_service import init_nacos_client

# 初始化Nacos客户端
nacos_client = init_nacos_client(
    server_addresses="127.0.0.1:8848",
    namespace="",
    username="nacos",
    password="nacos"
)

# 初始化配置管理器（启用自动刷新）
config_manager = init_config_manager_v2(
    nacos_client,
    enable_auto_refresh=True,    # 启用自动刷新
    refresh_interval=30          # 30秒检查一次
)
```

### 2. 添加配置变更监听器

```python
from multi_session_enhancement.nacos_config_manager_v2 import ConfigChangeListener

def on_config_change(data_id: str, new_config: dict):
    print(f"配置已更新: {data_id}")
    print(f"新配置: {new_config}")
    
    # 在这里添加配置变更后的处理逻辑
    # 例如：重新初始化客户端、更新全局变量等

# 创建监听器
listener = ConfigChangeListener(on_config_change)

# 添加到配置管理器
config_manager.add_config_listener("livetalking-api-config", listener)
```

### 3. 使用增强版应用

```bash
# 启动支持自动刷新的应用
python multi_session_enhancement/enhanced_app_v2.py --model musetalk --port 8010
```

## 📋 配置管理API

增强版应用提供了配置管理相关的API端点：

### 1. 获取配置状态
```bash
GET /api/config/status
```

响应示例：
```json
{
  "nacos_connected": true,
  "auto_refresh_enabled": true,
  "refresh_interval": 30,
  "cache_info": {
    "cache_size": 3,
    "cached_configs": [
      "livetalking-api-config:DEFAULT_GROUP",
      "livetalking-llm-config:DEFAULT_GROUP",
      "livetalking-tts-config:DEFAULT_GROUP"
    ],
    "cache_timeout": 300
  }
}
```

### 2. 强制刷新配置
```bash
POST /api/config/refresh
```

### 3. 获取当前配置
```bash
GET /api/config/current
```

## 🔧 配置变更处理

### 1. API配置变更
当API配置（如API密钥）发生变更时：
- 自动重新初始化相关的API客户端
- 通知所有活跃的session配置已更新
- 记录配置变更日志

### 2. LLM配置变更
当LLM配置发生变更时：
- 重新创建OpenAI客户端
- 更新模型参数（model、temperature等）
- 影响后续的LLM调用

### 3. TTS配置变更
当TTS配置发生变更时：
- 清除TTS客户端缓存
- 重新初始化TTS实例
- 更新语音合成参数

## 📝 实际使用示例

### 1. 在Nacos控制台修改配置

1. 登录Nacos控制台：`http://localhost:8848/nacos`
2. 进入配置管理 → 配置列表
3. 找到 `livetalking-api-config` 配置
4. 点击编辑，修改API密钥
5. 点击发布

### 2. 观察自动刷新效果

应用日志会显示：
```
2024-01-20 10:30:15 - INFO - 🔄 API配置已更新: livetalking-api-config
2024-01-20 10:30:15 - INFO - DashScope Key: sk-new-api-key...
2024-01-20 10:30:15 - INFO - 腾讯云 App ID: new-tencent-appid
2024-01-20 10:30:15 - INFO - LLM客户端重新初始化完成
```

### 3. 验证配置生效

```bash
# 检查配置状态
curl http://localhost:8010/api/config/current

# 强制刷新配置
curl -X POST http://localhost:8010/api/config/refresh
```

## ⚙️ 高级配置

### 1. 自定义刷新间隔

```python
config_manager = init_config_manager_v2(
    nacos_client,
    enable_auto_refresh=True,
    refresh_interval=10  # 10秒检查一次（更频繁）
)
```

### 2. 禁用自动刷新

```python
config_manager = init_config_manager_v2(
    nacos_client,
    enable_auto_refresh=False  # 禁用自动刷新
)

# 手动刷新
config_manager.force_refresh()
```

### 3. 自定义配置变更处理

```python
class CustomConfigListener:
    def on_api_config_change(self, data_id: str, new_config: dict):
        # 自定义API配置变更处理
        print("重新初始化API客户端...")
        
    def on_llm_config_change(self, data_id: str, new_config: dict):
        # 自定义LLM配置变更处理
        print("更新LLM参数...")

listener = CustomConfigListener()
api_listener = ConfigChangeListener(listener.on_api_config_change)
config_manager.add_config_listener("livetalking-api-config", api_listener)
```

## 🛠️ 故障排查

### 1. 自动刷新不工作
- 检查Nacos服务是否正常运行
- 验证网络连接是否正常
- 查看应用日志中的错误信息

### 2. 配置变更未生效
- 确认配置格式正确（JSON/YAML）
- 检查配置的Data ID和Group是否匹配
- 尝试手动强制刷新

### 3. 性能问题
- 适当调整刷新间隔（不要太频繁）
- 监控缓存使用情况
- 检查配置大小是否合理

## 📊 监控和日志

### 1. 配置变更日志
```
2024-01-20 10:30:15 - INFO - 检测到配置变更: livetalking-api-config
2024-01-20 10:30:15 - INFO - 🔄 API配置已更新: livetalking-api-config
2024-01-20 10:30:15 - INFO - 通知session 12345 配置变更: api
```

### 2. 缓存状态监控
```python
# 获取缓存信息
cache_info = config_manager.get_cache_info()
print(f"缓存大小: {cache_info['cache_size']}")
print(f"缓存配置: {cache_info['cached_configs']}")
```

### 3. 性能指标
- 配置刷新频率
- 缓存命中率
- 配置变更响应时间

## 🎉 总结

Nacos配置自动刷新功能为LiveTalking应用提供了：

1. **运行时配置更新**：无需重启即可应用新配置
2. **实时响应**：配置变更后立即生效
3. **高可用性**：降级保护确保服务稳定
4. **易于监控**：丰富的API和日志支持

通过这个功能，你可以：
- 动态调整API密钥而不影响服务
- 实时更新LLM模型参数
- 灵活配置TTS语音选项
- 提高运维效率和用户体验
