"""
Nacos配置管理器 - 替代环境变量读取
"""
import os
import json
import yaml
import logging
import time
import threading
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass, asdict
from threading import Lock, Thread
from nacos import NacosClient

logger = logging.getLogger(__name__)

@dataclass
class APIConfig:
    """API配置类"""
    # 阿里云DashScope配置
    dashscope_api_key: str = ""
    
    # 腾讯云配置
    tencent_appid: str = ""
    tencent_secret_key: str = ""
    tencent_secret_id: str = ""
    
    # 豆包/火山引擎配置
    doubao_appid: str = ""
    doubao_token: str = ""
    
    # OpenAI配置
    openai_api_key: str = ""
    openai_base_url: str = "https://api.openai.com/v1"
    
    # 其他TTS服务配置
    azure_speech_key: str = ""
    azure_speech_region: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'APIConfig':
        """从字典创建实例"""
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

@dataclass
class LLMConfig:
    """LLM配置类"""
    provider: str = "dashscope"
    api_key: str = ""
    base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    model: str = "qwen-plus"
    max_tokens: int = 8192
    temperature: float = 0.7
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LLMConfig':
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

@dataclass
class TTSConfig:
    """TTS配置类"""
    provider: str = "edgetts"
    api_key: str = ""
    app_id: str = ""
    secret_key: str = ""
    secret_id: str = ""
    region: str = ""
    voice: str = "zh-CN-YunxiaNeural"
    speed: float = 1.0
    volume: float = 1.0
    pitch: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TTSConfig':
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

class NacosConfigManager:
    """Nacos配置管理器"""
    
    def __init__(self, nacos_client: Optional[NacosClient] = None, 
                 server_addresses: str = "127.0.0.1:8848",
                 namespace: str = "",
                 username: str = "nacos",
                 password: str = "nacos"):
        """
        初始化Nacos配置管理器
        
        Args:
            nacos_client: 现有的Nacos客户端实例
            server_addresses: Nacos服务器地址
            namespace: 命名空间
            username: 用户名
            password: 密码
        """
        if nacos_client:
            self.client = nacos_client
        else:
            self.client = NacosClient(
                server_addresses=server_addresses,
                namespace=namespace,
                username=username,
                password=password
            )
        
        # 配置缓存
        self._config_cache = {}
        self._cache_timeout = 300  # 5分钟缓存
        self._last_update = {}
        
        # 默认配置组和数据ID
        self.default_group = "DEFAULT_GROUP"
        self.api_config_data_id = "livetalking-api-config"
        self.llm_config_data_id = "livetalking-llm-config"
        self.tts_config_data_id = "livetalking-tts-config"
        
    def _get_config_from_nacos(self, data_id: str, group: str = None) -> Optional[Dict[str, Any]]:
        """从Nacos获取配置"""
        if group is None:
            group = self.default_group
            
        try:
            config_content = self.client.get_config(data_id, group)
            if config_content:
                # 尝试解析JSON或YAML
                try:
                    return json.loads(config_content)
                except json.JSONDecodeError:
                    try:
                        return yaml.safe_load(config_content)
                    except yaml.YAMLError:
                        logger.error(f"无法解析配置内容: {data_id}")
                        return None
            return None
        except Exception as e:
            logger.error(f"从Nacos获取配置失败: {data_id}, 错误: {e}")
            return None
    
    def _get_cached_config(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存的配置"""
        import time
        current_time = time.time()
        
        if cache_key in self._config_cache:
            last_update = self._last_update.get(cache_key, 0)
            if current_time - last_update < self._cache_timeout:
                return self._config_cache[cache_key]
        
        return None
    
    def _set_cached_config(self, cache_key: str, config: Dict[str, Any]):
        """设置缓存的配置"""
        import time
        self._config_cache[cache_key] = config
        self._last_update[cache_key] = time.time()
    
    def get_api_config(self, group: str = None) -> APIConfig:
        """获取API配置"""
        cache_key = f"api_config_{group or self.default_group}"
        
        # 尝试从缓存获取
        cached_config = self._get_cached_config(cache_key)
        if cached_config:
            return APIConfig.from_dict(cached_config)
        
        # 从Nacos获取
        config_data = self._get_config_from_nacos(self.api_config_data_id, group)
        if config_data:
            self._set_cached_config(cache_key, config_data)
            return APIConfig.from_dict(config_data)
        
        # 降级到环境变量
        logger.warning("从Nacos获取API配置失败，降级到环境变量")
        return self._get_api_config_from_env()
    
    def get_llm_config(self, group: str = None) -> LLMConfig:
        """获取LLM配置"""
        cache_key = f"llm_config_{group or self.default_group}"
        
        cached_config = self._get_cached_config(cache_key)
        if cached_config:
            return LLMConfig.from_dict(cached_config)
        
        config_data = self._get_config_from_nacos(self.llm_config_data_id, group)
        if config_data:
            self._set_cached_config(cache_key, config_data)
            return LLMConfig.from_dict(config_data)
        
        logger.warning("从Nacos获取LLM配置失败，降级到环境变量")
        return self._get_llm_config_from_env()
    
    def get_tts_config(self, provider: str = "edgetts", group: str = None) -> TTSConfig:
        """获取TTS配置"""
        cache_key = f"tts_config_{provider}_{group or self.default_group}"
        
        cached_config = self._get_cached_config(cache_key)
        if cached_config:
            # 获取特定provider的配置
            provider_config = cached_config.get(provider, {})
            return TTSConfig.from_dict(provider_config)
        
        config_data = self._get_config_from_nacos(self.tts_config_data_id, group)
        if config_data:
            self._set_cached_config(cache_key, config_data)
            provider_config = config_data.get(provider, {})
            return TTSConfig.from_dict(provider_config)
        
        logger.warning(f"从Nacos获取TTS配置失败，降级到环境变量: {provider}")
        return self._get_tts_config_from_env(provider)
    
    def _get_api_config_from_env(self) -> APIConfig:
        """从环境变量获取API配置（降级方案）"""
        return APIConfig(
            dashscope_api_key=os.getenv("DASHSCOPE_API_KEY", ""),
            tencent_appid=os.getenv("TENCENT_APPID", ""),
            tencent_secret_key=os.getenv("TENCENT_SECRET_KEY", ""),
            tencent_secret_id=os.getenv("TENCENT_SECRET_ID", ""),
            doubao_appid=os.getenv("DOUBAO_APPID", ""),
            doubao_token=os.getenv("DOUBAO_TOKEN", ""),
            openai_api_key=os.getenv("OPENAI_API_KEY", ""),
            openai_base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
            azure_speech_key=os.getenv("AZURE_SPEECH_KEY", ""),
            azure_speech_region=os.getenv("AZURE_SPEECH_REGION", "")
        )
    
    def _get_llm_config_from_env(self) -> LLMConfig:
        """从环境变量获取LLM配置（降级方案）"""
        return LLMConfig(
            api_key=os.getenv("DASHSCOPE_API_KEY", ""),
            base_url=os.getenv("LLM_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
            model=os.getenv("LLM_MODEL", "qwen-plus")
        )
    
    def _get_tts_config_from_env(self, provider: str) -> TTSConfig:
        """从环境变量获取TTS配置（降级方案）"""
        if provider == "tencent":
            return TTSConfig(
                provider=provider,
                app_id=os.getenv("TENCENT_APPID", ""),
                secret_key=os.getenv("TENCENT_SECRET_KEY", ""),
                secret_id=os.getenv("TENCENT_SECRET_ID", "")
            )
        elif provider == "doubao":
            return TTSConfig(
                provider=provider,
                app_id=os.getenv("DOUBAO_APPID", ""),
                api_key=os.getenv("DOUBAO_TOKEN", "")
            )
        else:
            return TTSConfig(provider=provider)
    
    def publish_config(self, data_id: str, config_data: Dict[str, Any], 
                      group: str = None, config_type: str = "json") -> bool:
        """发布配置到Nacos"""
        if group is None:
            group = self.default_group
            
        try:
            if config_type == "json":
                content = json.dumps(config_data, ensure_ascii=False, indent=2)
            else:
                content = yaml.dump(config_data, allow_unicode=True, default_flow_style=False)
            
            result = self.client.publish_config(data_id, group, content)
            if result:
                # 清除缓存
                cache_key = f"{data_id.replace('-', '_')}_{group}"
                if cache_key in self._config_cache:
                    del self._config_cache[cache_key]
                logger.info(f"成功发布配置: {data_id}")
            return result
        except Exception as e:
            logger.error(f"发布配置失败: {data_id}, 错误: {e}")
            return False
    
    def clear_cache(self):
        """清除配置缓存"""
        self._config_cache.clear()
        self._last_update.clear()
        logger.info("配置缓存已清除")

# 全局配置管理器实例
_global_config_manager: Optional[NacosConfigManager] = None

def init_config_manager(nacos_client: Optional[NacosClient] = None, **kwargs) -> NacosConfigManager:
    """初始化全局配置管理器"""
    global _global_config_manager
    _global_config_manager = NacosConfigManager(nacos_client, **kwargs)
    return _global_config_manager

def get_config_manager() -> Optional[NacosConfigManager]:
    """获取全局配置管理器"""
    return _global_config_manager

def get_api_config() -> APIConfig:
    """获取API配置的便捷函数"""
    manager = get_config_manager()
    if manager:
        return manager.get_api_config()
    else:
        # 降级到环境变量
        return APIConfig(
            dashscope_api_key=os.getenv("DASHSCOPE_API_KEY", ""),
            tencent_appid=os.getenv("TENCENT_APPID", ""),
            tencent_secret_key=os.getenv("TENCENT_SECRET_KEY", ""),
            tencent_secret_id=os.getenv("TENCENT_SECRET_ID", ""),
            doubao_appid=os.getenv("DOUBAO_APPID", ""),
            doubao_token=os.getenv("DOUBAO_TOKEN", "")
        )

def get_llm_config() -> LLMConfig:
    """获取LLM配置的便捷函数"""
    manager = get_config_manager()
    if manager:
        return manager.get_llm_config()
    else:
        return LLMConfig(api_key=os.getenv("DASHSCOPE_API_KEY", ""))

def get_tts_config(provider: str = "edgetts") -> TTSConfig:
    """获取TTS配置的便捷函数"""
    manager = get_config_manager()
    if manager:
        return manager.get_tts_config(provider)
    else:
        if provider == "tencent":
            return TTSConfig(
                provider=provider,
                app_id=os.getenv("TENCENT_APPID", ""),
                secret_key=os.getenv("TENCENT_SECRET_KEY", ""),
                secret_id=os.getenv("TENCENT_SECRET_ID", "")
            )
        elif provider == "doubao":
            return TTSConfig(
                provider=provider,
                app_id=os.getenv("DOUBAO_APPID", ""),
                api_key=os.getenv("DOUBAO_TOKEN", "")
            )
        else:
            return TTSConfig(provider=provider)
