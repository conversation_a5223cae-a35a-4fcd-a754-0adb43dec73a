version: '3.8'
services:
  livetalking:
    image: livetalking:latest
    container_name: livetalking-app
    restart: always
    network_mode: host
    volumes:
      - nfs-models:/app/models
      - nfs-data:/app/data
    environment:
      - DASHSCOPE_API_KEY=sk-d3bebc7e9a584a93a4557e9c6752f237
      - TENCENT_APPID=1251115004
      - TENCENT_SECRET_KEY=d2JNDvQhkZAYjhR3RdbMwedWKuHQM5Zp
      - TENCENT_SECRET_ID=AKIDFNIzbAXq7WjiT0IMyTr10mNmz3xe7MNA
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

volumes:
  nfs-models:
    driver: local
    driver_opts:
      type: nfs
      # 修改为你的NFS服务器IP和路径
      o: addr=*************,rw,nfsvers=4
      device: ":/path/to/nfs/models"
  nfs-data:
    driver: local
    driver_opts:
      type: nfs
      # 修改为你的NFS服务器IP和路径
      o: addr=*************,rw,nfsvers=4
      device: ":/path/to/nfs/data"

# 使用方法：
# 1. 修改上面的NFS服务器IP和路径
# 2. 运行：docker-compose -f docker-compose-nfs.yml up -d
